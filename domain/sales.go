package domain

const (
	OrderStatusPending             = "pending"
	OrderStatusCancel              = "cancel"
	OrderStatusAccept              = "accept"
	OrderStatusReject              = "reject"
	OrderStatusPaymentVerification = "payment_verification"
	OrderStatusPaymentVerified     = "payment_verified"
	OrderStatusPaymentReject       = "payment_reject"
	OrderStatusReady               = "ready"
	OrderStatusTaken               = "taken"
	OrderStatusArrived             = "arrived"
	OrderStatusReceived            = "received"
	OrderStatusComplaint           = "complaint"
)

var OrderStatusList = []string{
	OrderStatusPending,
	OrderStatusCancel,
	OrderStatusAccept,
	OrderStatusReject,
	OrderStatusPaymentVerification,
	OrderStatusPaymentVerified,
	OrderStatusPaymentReject,
	OrderStatusReady,
	OrderStatusTaken,
	OrderStatusArrived,
	OrderStatusReceived,
	OrderStatusComplaint,
}

type Sales struct{}

type OrderSalesReqParam struct {
	LastSync     int64  `json:"last_sync,omitempty"`
	OrderSalesId string `json:"order_sales_id,omitempty"`
}

type OrderSalesUpdate struct {
	Status     string `json:"status,omitempty"`
	EmployeeId int    `json:"employee_id,omitempty"`
	Message    string `json:"message,omitempty"`
}

type SalesTagRequest struct {
	OutletId int `json:"outlet_id,omitempty"`
}

type SalesRequest struct {
	OutletId int
	LastSync int64
}
