package models

const (
	PaymentMethodQris = "qris"
)

type CreateBillingRequest struct {
	Id            string          `json:"id,omitempty"`
	AdminId       int64           `json:"admin_id,omitempty"`
	PaymentMethod string          `json:"payment_method,omitempty"`
	Amount        int             `json:"amount,omitempty"`
	Customer      BillingCustomer `json:"customer,omitempty"`
	CallbackUrl   string          `json:"callback_url,omitempty"`
}

type BillingCustomer struct {
	Name  string `json:"name,omitempty"`
	Email string `json:"email,omitempty"`
	Phone string `json:"phone,omitempty"`
}

type CreateBillingResponse struct {
	Qris          BillingQris `json:"qris,omitempty"`
	TransactionID string      `json:"transaction_id,omitempty"`
}

type BillingQris struct {
	URL string `json:"url"`
}

type BillingWithUser struct {
	Billing
	Admin BillingAdmin `json:"admin"`
}

type Billing struct {
	BillingId        int             `json:"billing_id"`
	Invoice          string          `json:"invoice"`
	Discount         int             `json:"discount"`
	IsPercent        int             `json:"is_percent"`
	DiscountNote     string          `json:"discount_note"`
	UniqueCode       int             `json:"unique_code"`
	AdminFkid        int             `json:"admin_fkid"`
	BillingNotes     string          `json:"billing_notes"`
	DetailTotal      int             `json:"detail_total"`
	TimeOrder        int64           `json:"time_order"`
	TimeOrderExpired int64           `json:"time_order_expired"`
	TimeConfirm      int64           `json:"time_confirm"`
	PaymentCode      string          `json:"payment_code"`
	PaymentType      string          `json:"payment_type"`
	BillingStatus    string          `json:"billing_status"`
	PaymentResult    string          `json:"payment_result"`
	PaymentUrl       string          `json:"payment_url"`
	PaymentInfo      interface{}     `json:"payment_info"`
	PdfUrl           interface{}     `json:"pdf_url"`
	PaidStatus       int             `json:"paid_status"`
	SysuserFkid      interface{}     `json:"sysuser_fkid"`
	DataCreated      int64           `json:"data_created"`
	DataUpdated      int64           `json:"data_updated"`
	StatusInfo       interface{}     `json:"status_info"`
	GeneratedBy      string          `json:"generated_by"`
	Detail           []BillingDetail `json:"detail"`
}

type BillingDetail struct {
	BillingDetailId  int    `json:"billing_detail_id"`
	BillingFkid      int    `json:"billing_fkid"`
	SysServiceFkid   int    `json:"sys_service_fkid"`
	Qty              int    `json:"qty"`
	ServicePeriod    int    `json:"service_period"`
	ServiceLengthDay int    `json:"service_length_day"`
	Price            int    `json:"price"`
	Discount         int    `json:"discount"`
	ServiceType      string `json:"service_type"`
	DiscountInfo     string `json:"discount_info"`
	SystemService
}

type SystemService struct {
	SysServiceId     int    `json:"sys_service_id"`
	Name             string `json:"name"`
	Price            int    `json:"price"`
	Discount         int    `json:"discount"`
	ServiceFeature   string `json:"service_feature"`
	ServiceLengthDay int    `json:"service_length_day"`
	ServicePeriodMax int    `json:"service_period_max"`
	ServiceType      string `json:"service_type"`
	ServiceSortorder int    `json:"service_sortorder"`
	DataCreated      int64  `json:"data_created"`
	DataModified     int64  `json:"data_modified"`
	DataStatus       int    `json:"data_status"`
}

type BillingAdmin struct {
	Name         string `json:"name"`
	Email        string `json:"email"`
	Phone        string `json:"phone"`
	UserType     string `json:"user_type"`
	UserId       string `json:"user_id"`
	BusinessId   string `json:"business_id"`
	AccountId    string `json:"account_id"`
	BusinessName string `json:"business_name"`
}
