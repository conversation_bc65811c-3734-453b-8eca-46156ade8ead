package models

type CloseShiftReport struct {
	Employee             map[string]interface{} `json:"employee,omitempty"`
	Admin                map[string]interface{} `json:"admin,omitempty"`
	OutletName           string                 `bson:"outlet_name" json:"outlet_name,omitempty"`
	EmployeeName         string                 `bson:"employee_name" json:"employee_name,omitempty"`
	ItemSalesQty         int                    `bson:"item_sales_qty" json:"item_sales_qty,omitempty"`
	ItemSalesTotal       int                    `bson:"item_sales_total" json:"item_sales_total,omitempty"`
	DiscTotal            int                    `bson:"disc_total" json:"disc_total,omitempty"`
	TotalCash            int                    `bson:"total_cash" json:"total_cash,omitempty"`
	TotalCard            int                    `bson:"total_card" json:"total_card,omitempty"`
	CardDetail           string                 `bson:"card_detail" json:"card_detail,omitempty"`
	BankDetail           []BankDetail           `bson:"bank_detail" json:"bank_detail,omitempty"`
	TotalPiutang         int                    `bson:"total_piutang" json:"total_piutang,omitempty"`
	TotalRefund          int                    `bson:"total_refund" json:"total_refund,omitempty"`
	TotalVoid            int                    `bson:"total_void" json:"total_void,omitempty"`
	TotalPax             int                    `bson:"total_pax" json:"total_pax,omitempty"`
	AvgPax               int                    `bson:"avg_pax" json:"avg_pax,omitempty"`
	BillCount            int                    `bson:"bill_count" json:"bill_count,omitempty"`
	AvgBill              int                    `bson:"avg_bill" json:"avg_bill,omitempty"`
	ShiftName            string                 `bson:"shift_name" json:"shift_name,omitempty"`
	Shift                Shift                  `bson:"shift" json:"shift,omitempty"`
	Warning              string                 `bson:"warning" json:"warning,omitempty"`
	Date                 string                 `bson:"date" json:"date,omitempty"`
	ActualCash           int                    `bson:"actual_cash" json:"actual_cash,omitempty"`
	ActualCard           int                    `bson:"actual_card" json:"actual_card,omitempty"`
	TotalOperationalCost int                    `bson:"total_operational_cost" json:"total_operational_cost,omitempty"`
	Outlet               Outlet                 `bson:"outlet,omitempty" json:"outlet,omitempty"`
	DateTime             int64                  `bson:"date_time,omitempty" json:"date_time,omitempty"`
	OpenShiftId          int64                  `bson:"open_shift_id,omitempty" json:"open_shift_id,omitempty"`
	CommissionPercentage string                 `bson:"commission_percentage,omitempty" json:"commission_percentage,omitempty"`
	CommissionTotal      int                    `bson:"commission_total,omitempty" json:"commission_total,omitempty"`
}

type Outlet struct {
	OutletName string `bson:"outlet_name" json:"outlet_name"`
	OutletId   int    `bson:"outlet_id" json:"outlet_id"`
}

type Shift struct {
	ShiftName string `bson:"shift_name" json:"shift_name"`
	ShiftId   int    `bson:"shift_id" json:"shift_id"`
}

type BankDetail struct {
	BankName string `bson:"bank_name" json:"bank_name"`
	Total    int    `bson:"total" json:"total"`
}

type BankDetailTemplate struct {
	BankName string
	Total    string
}
