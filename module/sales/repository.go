package sales

import (
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

type Repository interface {
	FetchSales(param domain.SalesRequest, user domain.UserSession) ([]map[string]interface{}, error)

	FetchSalesRefund(salesIds ...string) ([]map[string]interface{}, error)
	FetchSalesDetail(salesIds ...string) ([]map[string]interface{}, error)
	FetchSalesVoid(salesIds ...string) ([]map[string]interface{}, error)
	FetchSalesTax(salesIds ...string) ([]map[string]interface{}, error)
	FetchSalesPayment(salesIds ...string) ([]map[string]interface{}, error)
	FetchSalesPromotion(salesIds ...string) ([]map[string]interface{}, error)
	FetchSalesDetailPromotion(salesIds ...string) ([]map[string]interface{}, error)

	FetchSalesCart(id string, user domain.UserSession) (models.SalesCart, error)
	FetchSalesCartPayment(id string) ([]models.SalesCartPaymentEntity, error)
	CreateSalesPayment(payment models.SalesCartPaymentEntity) (int64, error)

	FetchOrderSales(param domain.OrderSalesReqParam, user domain.UserSession) ([]models.OrderSalesEntity, error)
	UpdateOrderSales(id string, param domain.OrderSalesUpdate, user domain.UserSession) error
	CountSales(openShiftId int) (int, error)

	UpdateSalesCart(cart models.SalesCart) error

	FetchBank(filter models.BankFilter) ([]models.Bank, error)
	AddBank(bank models.Bank) (int64, error)
	AddBankDetail(details ...models.BankDetailEntity) error
	FetchAdmin(adminId int) (models.AdminEntity, error)

	FetchCurrentShift(outletId int) (models.OpenShift, error)

	FetchDevices(outletId int) ([]models.DeviceEntity, error)

	FetchPaymentNotification(transactionID string) (*[]models.PaymentNotificationEntity, error)

	FetchSalesTag(param *domain.SalesTagRequest, user *domain.UserSession) (*[]models.SalesTagEntity, error)

	FetchSalesById(salesId string) (models.SalesEntity, error)
	FetchSalesDetailWithProduct(salesId string) ([]map[string]interface{}, error)
	FetchOutletById(outletId int) (map[string]interface{}, error)
	FetchAppCrmConfig(adminId int) (models.CrmAppConfig, error)
	FetchPointEarned(salesId string) (int, error)

	//crm transaction config
	FetchTransactionConfig(adminId int64) ([]map[string]interface{}, error)
}

type BillingRepository interface {
	CreatePayment(billing models.CreateBillingRequest) (models.CreateBillingResponse, error)
}
