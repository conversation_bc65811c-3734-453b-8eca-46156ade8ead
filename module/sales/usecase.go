package sales

import (
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

const PaymentIdPrefix = "CART_" //used for direct payment in pos devices

type UseCase interface {
	FetchSales(param domain.SalesRequest, user domain.UserSession) (models.ResponseArray, error)

	CreateSalesPayment(salesId string, user domain.UserSession) (map[string]interface{}, error)
	FetchSalesPayment(salesId string, user domain.UserSession) (models.SalesCartPaymentEntity, error)

	//order sales
	FetchOrderSales(param domain.OrderSalesReqParam, user domain.UserSession) ([]models.OrderSalesEntity, error)
	UpdateOrderSales(orderId string, param domain.OrderSalesUpdate, user domain.UserSession) error

	FetchSalesTag(param *domain.SalesTagRequest, user *domain.UserSession) (*[]models.SalesTagEntity, error)

	SendReceipt(salesId string) error

	//transaction config
	FetchTransactionConfig(user domain.UserSession) (map[string]interface{}, error)
}
