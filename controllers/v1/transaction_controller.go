package v1

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"html/template"
	"math"
	"math/rand"
	"os"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"sync"
	templateText "text/template"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/go-sql-driver/mysql"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/format"
	"gitlab.com/uniqdev/backend/api-pos/core/google"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/core/utils/array"
	"gitlab.com/uniqdev/backend/api-pos/integration/behave"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"gitlab.com/uniqdev/backend/api-pos/module/lang"
)

var syncSalesProcess = make(map[string]bool)

func AddSalesFromOrder(ctx *fasthttp.RequestCtx) {
	orderSalesId := ctx.PostArgs().Peek("order_sales_id")
	log.Info("request to add sales from order: %s", orderSalesId)

	//response := HandleOrderSales(string(orderSalesId))
	//log.IfError(json.NewEncoder(ctx).Encode(response))
	//_ = json.NewEncoder(ctx).Encode(map[string]any{
	//	"message": fmt.Sprintf("order_sales with id %s has been handled", string(orderSalesId)),
	//})
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success"})
}

// sending notif to POS device about new transaction
func SendNotificationOrder(orderSalesId string) {
	sql := `SELECT d.firebase_token, os.status 
	from order_sales os 
join devices d on d.outlet_fkid=os.outlet_fkid
where os.order_sales_id=? and device_status='on' `
	devices, err := db.QueryArray(sql, orderSalesId)
	log.Info("total device %v from orderSalesId %v", len(devices), orderSalesId)
	if log.IfError(err) {
		log.Info("failed get devices: %v", err)
		return
	}

	for _, device := range devices {
		token := strings.TrimSpace(cast.ToString(device["firebase_token"]))
		if token == "" {
			continue
		}
		if cast.ToString(device["status"]) != "pending" {
			continue
		}
		google.SendNotification(google.NotificationData{
			Token: token,
			Data: map[string]string{
				"order_sales_id": orderSalesId,
				"type":           "order_sales",
				"title":          "Konfirmasi Pesanan Baru",
				"message":        fmt.Sprintf("%s butuh konfirmasi", orderSalesId),
			},
		})
	}
}

func getOrAddServiceByName(serviceName string, total int, adminId any) models.Taxes {
	service, err := db.Query("select gratuity_id from gratuity  where name = ? and admin_fkid = ?", serviceName, adminId)
	log.IfError(err)

	result := models.Taxes{
		Category: "service",
		Name:     serviceName,
		Type:     "nominal",
		Total:    total,
		Value:    total,
	}

	if len(service) == 0 {
		resp, err := db.Insert("gratuity", map[string]any{
			"name":          serviceName,
			"tax_category":  result.Category,
			"tax_status":    "temp_deactive",
			"tax_type":      result.Type,
			"jumlah":        0,
			"admin_fkid":    adminId,
			"data_created":  time.Now().Unix() * 1000,
			"data_modified": time.Now().Unix() * 1000,
			"data_status":   "off",
		})
		log.IfError(err)
		id, _ := resp.LastInsertId()
		result.ID = utils.ToInt(id)
	} else {
		result.ID = utils.ToInt(service["gratuity_id"])
	}

	return result
}

func NewSales(ctx *fasthttp.RequestCtx) {
	adminId := ctx.Request.Header.Peek("admin_id")
	deviceId := ctx.Request.Header.Peek("Device")

	// fmt.Println("sales -> ", string(ctx.PostBody()))
	log.Info("sales: %v", string(ctx.PostBody()))

	sales := models.Sale{}
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&sales)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		fmt.Println("invalid sales data --> ", string(ctx.PostBody()))
		return
	}

	// log.Info("%v header: %v", sales.NoNota, ctx.Request.Header.String())
	log.Info("salesHeader: %v, userAgent: %s", sales.NoNota, ctx.Request.Header.Peek("User-Agent"))

	if strings.TrimSpace(sales.Payment) == "" {
		payments := make([]string, 0)
		for _, payment := range sales.Payments {
			payments = append(payments, payment.Method)
		}
		sales.Payment = strings.Join(payments, ",")
	}

	if strings.TrimSpace(sales.Payment) == "INSTANT PAYMENT" && os.Getenv("ENV") == "development" {
		log.IfError(fmt.Errorf("skip err instant payment save, id: %s", sales.NoNota))
		fmt.Println("skip: ", string(ctx.PostBody()))
		json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true})
		return
	} else if strings.TrimSpace(sales.Payment) == "INSTANT PAYMENT" {
		sales.Payment = "CASH"
		sales.Payments[0].Method = "CASH"
		log.IfError(fmt.Errorf("change payment method to cash: '%s'", sales.NoNota))
	}

	//if string(adminId) == "10" && sales.OutletID == 43 {
	//	for i, order := range sales.OrderList {
	//		if order.Product.ProductDetailID == 42150 {
	//			sales.OrderList[i].Product.ProductDetailID = 42164
	//			sales.OrderList[i].Product.ProductID = 13598
	//			sales.OrderList[i].Product.ProductFkid = 13598
	//		}
	//	}
	//}

	//handle if openshift id null
	if sales.OpenShiftID == 0 {
		// sales.OpenShiftID = 52067
		// start := time.Unix(0, sales.TimeCreated*int64(time.Millisecond)).Add(-1 * time.Hour)
		// end := time.Unix(0, sales.TimeCreated*int64(time.Millisecond)).Add(1 * time.Hour)
		// sql := `SELECT open_shift_fkid as open_shift_id from sales where time_created BETWEEN ? and ? and outlet_fkid=? limit 1`
		// recordedSales, err := db.Query(sql, start, end, sales.OutletID)

		sql := `SELECT open_shift_id from open_shift where outlet_fkid=? and time_open < ? order by time_open desc limit 1`
		recordedSales, err := db.Query(sql, sales.OutletID, sales.TimeCreated)
		if id := cast.ToInt(recordedSales["open_shift_id"]); !log.IfError(err) && id > 0 {
			log.Info("openShift '%v' changed to: %v", sales.NoNota, id)
			log.Info(fmt.Sprintf("openShift '%v' should changed to: %v", sales.NoNota, id))
			sales.OpenShiftID = id
		}
	}

	//validation
	if strings.HasPrefix(string(ctx.Request.Header.Peek("User-Agent")), "Dart") {
		if len(sales.Payments) == 0 {
			ctx.SetStatusCode(fasthttp.StatusBadRequest)
			json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "payments is required"})
			return
		}
	}

	response, err := SaveSales(sales, string(adminId), string(deviceId))
	if err != nil {
		if strings.HasPrefix(err.Error(), "Error 1452: Cannot add or update a child row") || strings.Contains(err.Error(), "Duplicate entry") {
			log.Info("failed saving sales %v: %v", sales.NoNota, string(ctx.PostBody()))
		} else {
			log.IfErrorSetStatus(ctx, err)
			return
		}
	}

	log.IfError(json.NewEncoder(ctx).Encode(response))
}

func SaveSales(sales models.Sale, adminId, deviceId string) (models.ResponseAny, error) {
	millis := time.Now().Unix() * 1000
	response := models.ResponseAny{Millis: millis}
	responseData := make(map[string]any)

	timeStart := time.Now()
	log.Debug("%s (%s) - %d (%s) %s : %s", sales.NoNota, sales.Status, sales.OutletID, adminId, deviceId, utils.SimplyToJson(sales))

	if syncSalesProcess[sales.NoNota] {
		log.Warn("sales '%s' in progress to sync", sales.NoNota)
		response.Message = "sales is in progress to sync"
		response.Status = false
		//_ = json.NewEncoder(ctx).Encode(response)
		return response, nil
	}
	syncSalesProcess[sales.NoNota] = true
	defer func(noNota string) {
		syncSalesProcess[noNota] = false
	}(sales.NoNota)

	if strings.TrimSpace(sales.MemberId) == "" && sales.MemberDetail.MemberID != "" {
		sales.MemberId = sales.MemberDetail.MemberID
		log.Info("set memberId to : %v", sales.MemberId)
	}

	var err error
	//check if sales already inserted to table sales
	count := make(map[string]any)
	if strings.ToLower(sales.Status) == "refund" {
		count, err = db.Query("SELECT sales_id,status  FROM sales WHERE sales_id=? LIMIT 1", sales.NoNota)
		log.IfError(err)
	}

	//if sales already inserted, then we only update the status
	if strings.ToLower(sales.Status) == "refund" && len(count) > 0 {
		log.Info("%s - updating to refund...", sales.NoNota)
		//res, err := db.GetDb().Exec("UPDATE sales SET status=?, time_modified=? WHERE sales_id=?", sales.Status, millis, sales.NoNota)
		res, err := db.Update("sales", map[string]any{
			"status":        sales.Status,
			"time_modified": millis,
		}, "sales_id = ?", sales.NoNota)

		if err != nil {
			errCode := utils.ToInt(utils.GetErrCodeSQL(err.Error()))
			log.Info("err code: %d", errCode)
			if errCode == utils.ERR_SQL_LOCK_TIMEOUT || errCode == utils.ERR_SQL_DEADLOCK {
				response.Status = false
				response.Message = err.Error()
				return response, err
			}
		}
		if log.IfError(err) {
			return response, err
		}
		row, _ := res.RowsAffected()
		if row > 0 {
			response.Message = "Data successfully updated!"
		} else {
			response.Message = "No data updated!"
		}

		//if not inserted in table 'sales_refund' then do insert
		//count, err = db.Query("SELECT sales_refund_id FROM sales_refund WHERE sales_fkid=?", sales.NoNota)
		//log.IfError(err)
		//
		//if len(count) <= 0 {
		//	_, err = db.Insert("sales_refund", map[string]any{
		//		"sales_fkid":    sales.NoNota,
		//		"grand_total":   sales.GrandTotal,
		//		"employee_fkid": sales.EmployeeID,
		//		"time_created":  sales.TimeCreated,
		//		"time_modified": time.Now().Unix() * 1000,
		//	})
		//	if log.IfErrorSetStatus(ctx, err) {
		//		return
		//	}
		//}
	} else {
		log.Info("saving sales '%s' ...", sales.NoNota)

		sales.Customer = utils.ReplaceEmoji(sales.Customer)
		if len(sales.Customer) > 25 {
			sales.Customer = sales.Customer[0:25]
		}
		tgl, _ := date(strconv.FormatInt(sales.TimeCreated/1000, 10))

		if len(sales.ReceiptReceiver) < 10 {
			sales.ReceiptReceiver = ""
		}

		//if promotion not meet requirement (min order) : remove it
		if len(sales.Promotions) > 0 {
			if sales.Promotions[0].MinOrder > sales.GrandTotal {
				//log.Info("removing promotion code : %s", sales.Promotions[0].Code)
				//go removePromo(sales.Promotions[0].Code, sales.Promotions[0].Source, sales.OutletID)
				//sales.Promotions = nil
			}
		}

		//validate voucher & disc
		if sales.Discount.VoucherNominal < 0 {
			sales.Discount.VoucherNominal = 0
		}
		if sales.Discount.DiscountNominal < 0 {
			sales.Discount.DiscountNominal = 0
		}

		if len(sales.Table) > 5 {
			sales.Table = sales.Table[:5]
		}
		sales.Table = strings.TrimSpace(sales.Table)

		dataSales := map[string]any{
			"sales_id":         sales.NoNota,
			"payment":          sales.Payment,
			"dining_table":     sales.Table,
			"customer_name":    strings.TrimSpace(sales.Customer),
			"qty_customers":    sales.CustomersQty,
			"outlet_fkid":      sales.OutletID,
			"status":           sales.Status,
			"time_prediction":  sales.TimePrediction,
			"open_shift_fkid":  sales.OpenShiftID,
			"date_created":     tgl,
			"date_modified":    tgl,
			"time_created":     sales.TimeCreated,
			"time_modified":    time.Now().Unix() * 1000,
			"discount":         sales.Discount.DiscountNominal,
			"discount_info":    strings.TrimSpace(sales.Discount.DiscountInfo),
			"voucher":          sales.Discount.VoucherNominal,
			"voucher_info":     strings.TrimSpace(sales.Discount.VoucherInfo),
			"grand_total":      sales.GrandTotal,
			"display_nota":     sales.DisplayNota,
			"receipt_receiver": strings.TrimSpace(sales.ReceiptReceiver),
			"sales_tag_fkid":   sales.SalesTag.SalesTagID,
		}

		dataSales = array.RemoveEmpty(dataSales)

		if sales.EmployeeID > 0 {
			dataSales["employee_fkid"] = sales.EmployeeID
		}

		//fmt.Println("#Sales. Member : ", sales.MemberId, sales.MemberDetail.Source)
		if sales.MemberId != "" && sales.MemberDetail.Source != "behave" {
			dataSales["member_fkid"] = sales.MemberId
		} else if sales.MemberDetail.Source == "behave" {
			memberBehave, err := db.Query("select member_fkid from member_behave where info like ?", "%"+sales.MemberId+"%")
			fmt.Println("Behave size : ", len(memberBehave))
			log.IfError(err)
			if len(memberBehave) > 0 {
				dataSales["member_fkid"] = memberBehave["member_fkid"]
			}
		}

		shouldUpdatePoint := true
		salesVoid := make(map[int64]int)
		salesVoidQty := make(map[int64]int)
		jmlHrgTotalDisc := 0
		jmlHrgTotalVouc := 0
		subtotalAll := 0
		totalPromoSales := 0
		subtotalByProduct := make(map[int]int)
		salesPromotionFkids := make(map[int]int64)
		productDetailIds := make([]any, 0)

		calculate := func(order models.Order) {
			subtotalAll += cast.AbsInt(order.SubTotal)
			subtotalByProduct[order.Product.ProductDetailID] += cast.AbsInt(order.SubTotal)

			if order.Product.Discount == "on" {
				jmlHrgTotalDisc += order.SubTotal
			}
			if order.Product.Voucher == "on" {
				jmlHrgTotalVouc += order.SubTotal
			}
			if order.IsItemVoid {
				salesVoid[order.VoidParentId] += cast.AbsInt(order.SubTotal)
				salesVoidQty[order.VoidParentId] += cast.AbsInt(order.Qty)
			}
		}

		for _, order := range sales.OrderList {
			productDetailIds = append(productDetailIds, order.Product.ProductDetailID)
			calculate(order)
			for _, extra := range order.Extra {
				productDetailIds = append(productDetailIds, extra.Product.ProductDetailID)
				extra.IsItemVoid = order.IsItemVoid
				calculate(extra)
			}
		}

		for _, promo := range sales.Promotions {
			if (promo.DiscountType == "nota" || promo.TypeId == 15) && promo.PromoNominal > 0 {
				totalPromoSales += promo.PromoNominal
			}
		}

		// commissionMap := make(map[int]map[string]any)
		logSales := make([]string, 0)

		err = db.WithTransaction(func(db db.Transaction) error {
			_, err = db.Insert("sales", dataSales)

			detailIds := make(map[string]int64)
			extraIds := make(map[string]int64)
			totalTaxIdentic := make(map[int]int)
			warning := ""
			keyExtra := "%d-void-%d"

			for _, order := range sales.OrderList {
				//fix temp
				//if  order.Product.ProductDetailID == 104113 {
				//	order.Product.ProductDetailID = 104252
				//	order.Product.ProductID = 45717
				//}

				promotionValue := order.Promotion.PromotionValue
				if order.Promotion.PromotionTypeFkid > 0 || order.Promotion.PromotionId > 0 {
					promotionValue = (order.Product.PriceSell * order.Qty) - cast.AbsInt(order.SubTotal)
					if promotionValue < 0 {
						log.Warn("promotion value is : %d | force set to zero ", promotionValue)
						promotionValue = 0
					}
					log.Info("promotion value : %d | (%d x %d) - %d", promotionValue, order.Product.PriceSell, order.Qty, order.SubTotal)
					if order.Promotion.PromotionValue > 0 && promotionValue != order.Promotion.PromotionValue {
						log.Warn(fmt.Sprintf("promotion value calculation not same : %d (new) VS %d (%s)", promotionValue, order.Promotion.PromotionValue, sales.NoNota))
					}
					order.Price = order.Product.PriceSell
					order.SubTotal = order.Price * order.Qty
				}

				var detailId int64
				if order.IsItemVoid {
					salesVoidData := map[string]any{
						"sales_fkid":          sales.NoNota,
						"time_created":        order.TmpID,
						"product_fkid":        order.Product.ProductID,
						"product_detail_fkid": order.Product.ProductDetailID,
						"qty":                 order.Qty,
						"info":                strings.TrimSpace(order.VoidInfo),
						"price":               cast.AbsInt(order.Price) * -1,
						"discount":            cast.AbsInt(order.Discount.DiscountNominal),
						"sub_total":           cast.AbsInt(order.SubTotal) * -1,
						"employee_auth_fkid":  order.VoidEmployeeAuthId,
					}

					if order.EmployeeId > 0 {
						salesVoidData["employee_fkid"] = order.EmployeeId
					}

					if detailIds[utils.ToString(order.VoidParentId)] > 0 {
						detailId = detailIds[utils.ToString(order.VoidParentId)]
						salesVoidData["sales_detail_fkid"] = detailId
					} else {
						log.Error("WARN! can not get sales_detail_fkid, orderId: %d - parent id: %v", order.TmpID, order.VoidParentId)
						log.Info("detailIds: --> %s", utils.SimplyToJson(detailIds))
					}

					resp, _ := db.Insert("sales_void", salesVoidData)

					parentId, _ := resp.LastInsertId()

					//insert for extra
					for _, extra := range order.Extra {
						salesVoidExtra := map[string]any{
							"sales_fkid":          sales.NoNota,
							"time_created":        extra.TmpID,
							"product_fkid":        extra.Product.ProductID,
							"product_detail_fkid": extra.Product.ProductDetailID,
							"qty":                 extra.Qty,
							"info":                strings.TrimSpace(extra.VoidInfo),
							"discount":            extra.Discount.DiscountNominal,
							"price":               extra.Price,
							"sub_total":           extra.SubTotal,
							"parent":              parentId,
							"employee_auth_fkid":  order.VoidEmployeeAuthId,
						}

						if order.EmployeeId > 0 {
							salesVoidExtra["employee_fkid"] = order.EmployeeId
						}

						detailIdExtra := extraIds[fmt.Sprintf(keyExtra, extra.VoidParentId, extra.Product.ProductDetailID)]
						if detailIdExtra > 0 {
							salesVoidExtra["sales_detail_fkid"] = detailIdExtra
						}

						_, err = db.Insert("sales_void", salesVoidExtra)
					}

					//insert promotion
					if order.Promotion.PromotionId > 0 {
						_, err = db.Insert("sales_detail_promotion", map[string]any{
							//"sales_detail_fkid": detailId,
							"sales_void_fkid": parentId,
							"sales_fkid":      sales.NoNota,
							"promotion_fkid":  order.Promotion.PromotionId,
							"promotion_value": cast.AbsInt(promotionValue) * -1,
						})
					}
				} else {
					if order.Qty < 0 {
						warning += fmt.Sprintf("Qty : %d | ", order.Qty)
						order.Qty = order.Qty * -1
					}
					if order.SubTotal < 0 {
						warning += fmt.Sprintf("Subtotal : %d | ", order.SubTotal)
						order.SubTotal = order.SubTotal * -1
					}

					if order.Qty < 0 || order.SubTotal < 0 {
						warning += fmt.Sprintf("'%s' | id : %d | detailId : %d", order.Product.Name, order.Product.ProductID, order.Product.ProductDetailID)
					}

					totalCommissionCustomer := 0
					totalCommissionStaff := 0
					// if commission, ok := commissionMap[order.Product.ProductDetailID]; ok {
					// 	log.Info("commission : %v", commission)
					// 	totalCommissionCustomer = utils.ToInt(commission["commission_customer"]) * order.Qty
					// 	if commission["commission_customer_type"] == "percent" {
					// 		totalCommissionCustomer = int(float64(order.SubTotal-salesVoid[order.TmpID]) / float64(100) * utils.ToFloat(commission["commission_customer"]))
					// 	}

					// 	totalCommissionStaff = utils.ToInt(commission["commission_staff"]) * order.Qty
					// 	if commission["commission_staff_type"] == "percent" {
					// 		totalCommissionStaff = int(float64(order.SubTotal-salesVoid[order.TmpID]) / float64(100) * utils.ToFloat(commission["commission_staff"]))
					// 	}
					// }

					timeCreated := order.TmpID
					//handle if time from device is inaccurate
					if order.TmpID > time.Now().Unix()*1000 || order.TmpID <= 0 {
						if sales.TimeCreated < time.Now().Unix()*1000 && sales.TimeCreated > 0 {
							timeCreated = sales.TimeCreated
						} else {
							timeCreated = time.Now().Unix() * 1000
						}
					}
					salesDetail := map[string]any{
						"sales_fkid":          sales.NoNota,
						"time_created":        timeCreated,
						"product_fkid":        order.Product.ProductID,
						"product_detail_fkid": order.Product.ProductDetailID,
						"qty":                 order.Qty,
						"price_buy":           order.Product.PriceBuy,
						"price":               order.Price,
						"sub_total":           order.SubTotal,
						"discount":            order.Discount.DiscountNominal,
						"discount_info":       strings.TrimSpace(order.Discount.DiscountInfo),
						"price_add":           order.PriceAdd,
						// "note":                utils.ReplaceEmoji(strings.TrimSpace(order.Note)),
						"commission_staff":    totalCommissionStaff,
						"commission_customer": totalCommissionCustomer,
					}

					if order.EmployeeId > 0 {
						salesDetail["employee_fkid"] = order.EmployeeId
					}

					if note := utils.ReplaceEmoji(strings.TrimSpace(order.Note)); len(note) > 0 {
						salesDetail["note"] = note
					}

					//log.Info("INFO - %s \nORDER --> %s", note, utils.SimplyToJson(order))

					res, _ := db.Insert("sales_detail", salesDetail)

					detailId, err = res.LastInsertId()
					detailIds[utils.ToString(order.TmpID)] = detailId

					//insert into sales_detail_discount
					//rumus => harga/jumlah harga total * discount nominal
					disc := float32(0)
					if sales.Discount.DiscountNominal > 0 && order.Product.Discount == "on" {
						if sales.Discount.DiscountType == "nominal" {
							if jmlHrgTotalDisc == 0 {
								log.Info("jmlHrgTotalDisc is %d, set to : %d", jmlHrgTotalDisc, order.SubTotal)
								jmlHrgTotalDisc = order.SubTotal
							}
							disc = (float32(order.SubTotal-promotionValue-order.Discount.DiscountNominal-salesVoid[order.TmpID]) / float32(jmlHrgTotalDisc)) * float32(sales.Discount.DiscountNominal)
							logSales = append(logSales, fmt.Sprintf("[DISC] (%d - %d - %d) / %d * %d = %f", order.SubTotal, promotionValue, salesVoid[order.TmpID], jmlHrgTotalDisc, sales.Discount.DiscountNominal, disc))
						} else {
							disc = (float32(sales.Discount.Discount) / float32(100)) * float32(order.SubTotal-promotionValue-order.Discount.DiscountNominal-salesVoid[order.TmpID])
							logSales = append(logSales, fmt.Sprintf("[DISC] (%d / 100) * (%d - %d - %d) = %v", sales.Discount.Discount, order.SubTotal, promotionValue, salesVoid[order.TmpID], disc))
						}

						if disc > 0 {
							db.Insert("sales_detail_discount", map[string]any{
								"sales_detail_fkid": detailId,
								"type":              "discount",
								"total":             disc,
							})
						}
					}

					vouch := float32(0)
					if sales.Discount.VoucherNominal > 0 && order.Product.Voucher == "on" {
						if sales.Discount.VoucherType == "nominal" {
							if jmlHrgTotalVouc == 0 {
								log.Info("jmlHrgTotalVouc is %d, set to : %d", jmlHrgTotalVouc, order.SubTotal)
								jmlHrgTotalVouc = order.SubTotal
							}
							log.Info("[VOUCH] (%d - %d - %d) / %d * %d", order.SubTotal, promotionValue, salesVoid[order.TmpID], jmlHrgTotalVouc, sales.Discount.VoucherNominal)
							vouch = float32(order.SubTotal-order.Discount.VoucherNominal-promotionValue-salesVoid[order.TmpID]) / float32(jmlHrgTotalVouc) * float32(sales.Discount.VoucherNominal)
						} else {
							vouch = (float32(sales.Discount.Voucher) / float32(100)) * float32(order.SubTotal-order.Discount.VoucherNominal-promotionValue-salesVoid[order.TmpID])
							logSales = append(logSales, fmt.Sprintf("[VOUCH] (%d / 100) * (%d - %d - %d) = %v", sales.Discount.Voucher, order.SubTotal, promotionValue, salesVoid[order.TmpID], vouch))
						}
						if vouch > 0 {
							_, err = db.Insert("sales_detail_discount", map[string]any{
								"sales_detail_fkid": detailId,
								"type":              "voucher",
								"total":             vouch,
							})
						}
					}

					for _, tax := range order.Taxes {
						//skip if grand total is zero
						if sales.GrandTotal == 0 {
							continue
						}
						total := tax.Total
						//if type is nominal, divide proportionally
						if tax.Type == "nominal" {
							grandTotalWithSameTax := totalTaxIdentic[tax.ID]
							if grandTotalWithSameTax == 0 {
								for _, orderLoop := range sales.OrderList {
									for _, taxLoop := range orderLoop.Taxes {
										if taxLoop.ID == tax.ID {
											grandTotalWithSameTax += orderLoop.SubTotal
											break
										}
									}

									for _, extraLoop := range orderLoop.Extra {
										for _, taxLoop := range extraLoop.Taxes {
											if taxLoop.ID == tax.ID {
												grandTotalWithSameTax += extraLoop.SubTotal
												break
											}
										}
									}
								}
								totalTaxIdentic[tax.ID] = grandTotalWithSameTax //save, so we dont need to calculate again in the next loop
							}

							total = int((float32(order.SubTotal-promotionValue-salesVoid[order.TmpID]) / float32(grandTotalWithSameTax)) * float32(tax.Value))
							log.Info("Tax '%s' - value %d (%s) | client calculation : %d | server calculation %d ", tax.Name, tax.Value, tax.Type, tax.Total, total)
							log.Info("(%d - %d - %d) / %d * %d = %d", order.SubTotal, promotionValue, salesVoid[order.TmpID], grandTotalWithSameTax, tax.Value, total)
						} else {
							//promo sales: all promo which not per item, but for one sales, like discount nota
							subtotal := order.SubTotal - order.Discount.DiscountNominal - promotionValue - salesVoid[order.TmpID]
							promoSales := (float32(subtotal) / float32(subtotalAll)) * float32(totalPromoSales)
							logSales = append(logSales, fmt.Sprintf("promoSales: %v, totalPromoSales: %v, subtotalAll: %v, subtotal: %v", promoSales, totalPromoSales, subtotalAll, subtotal))

							total = int((float32(tax.Value) / float32(100)) * ((float32(order.SubTotal - order.Discount.DiscountNominal - promotionValue - salesVoid[order.TmpID] - int(promoSales))) - disc - vouch))
							logSales = append(logSales, fmt.Sprintf("Tax '%s' - value %d (%s) | client calculation : %d | server calculation %d ", tax.Name, tax.Value, tax.Type, tax.Total, total))
							logSales = append(logSales, fmt.Sprintf("%d / 100 * (%d - %d  - %d - %d - %d - %d - %v) = %d", tax.Value, order.SubTotal, order.Discount.DiscountNominal, promotionValue, salesVoid[order.TmpID], int(disc), int(vouch), promoSales, total))
							if tax.Total != total {
								log.Warn("tax calculation not same '%s'. %d (new) VS %d. detail tax : %v\n", sales.NoNota, total, tax.Total, tax)
							}
						}

						if total < 0 {
							log.Error("sales_detail_tax error. total is minus. Total : %d - %d (%s) - subtotal : %d | salesId : %s - detailId : %d | grandTotal: %v", total, tax.Value, tax.Type, order.SubTotal, sales.NoNota, detailId, sales.GrandTotal)
							total = 0
						}

						if total > 0 {
							_, err = db.Insert("sales_detail_tax", map[string]any{
								"tax_fkid":          tax.ID,
								"total":             total,
								"sales_detail_fkid": detailId,
								"category":          tax.Category,
							})
						}
					}

					//insert for extra
					for _, extra := range order.Extra {
						if order.IsItemVoid {
							_, err = db.Insert("sales_void", map[string]any{
								"sales_fkid":          sales.NoNota,
								"time_created":        extra.TmpID,
								"product_fkid":        extra.Product.ProductID,
								"product_detail_fkid": extra.Product.ProductDetailID,
								"qty":                 extra.Qty,
								"info":                strings.TrimSpace(extra.VoidInfo),
								"price":               extra.Price,
								"sub_total":           extra.SubTotal,
								"parent":              detailId,
							})
						} else {
							salesDetailExtra := map[string]any{
								"sales_fkid":          sales.NoNota,
								"time_created":        extra.TmpID,
								"product_fkid":        extra.Product.ProductID,
								"product_detail_fkid": extra.Product.ProductDetailID,
								"qty":                 extra.Qty,
								"price_buy":           extra.Product.PriceBuy,
								"price":               extra.Price,
								"sub_total":           extra.SubTotal,
								"discount":            extra.Discount.DiscountNominal,
								"discount_info":       strings.TrimSpace(extra.Discount.DiscountInfo),
								"parent":              detailId,
								"employee_fkid":       extra.EmployeeId,
							}

							if extra.ExtraType != "" {
								salesDetailExtra["child_type"] = extra.ExtraType
							}

							res, _ := db.Insert("sales_detail", salesDetailExtra)

							extraId, _ := res.LastInsertId()
							extraIds[fmt.Sprintf(keyExtra, extra.TmpID, extra.Product.ProductDetailID)] = extraId

							for _, tax := range extra.Taxes {
								total := tax.Total
								//if type is nominal, divide proportionally
								if tax.Type == "nominal" {
									grandTotalWithSameTax := totalTaxIdentic[tax.ID]
									if grandTotalWithSameTax == 0 {
										for _, orderLoop := range sales.OrderList {
											for _, taxLoop := range orderLoop.Taxes {
												if taxLoop.ID == tax.ID {
													grandTotalWithSameTax += orderLoop.SubTotal
													break
												}
											}
											for _, extraLoop := range orderLoop.Extra {
												for _, taxLoop := range extraLoop.Taxes {
													if taxLoop.ID == tax.ID {
														grandTotalWithSameTax += extraLoop.SubTotal
														break
													}
												}
											}
										}
										totalTaxIdentic[tax.ID] = grandTotalWithSameTax //save, so we dont need to calculate again in the next loop
									}

									total = int((float32(extra.SubTotal-salesVoid[extra.TmpID]) / float32(grandTotalWithSameTax)) * float32(tax.Value))
									log.Info("[EXTRA] (%d - %d) / %d * %d", extra.SubTotal, salesVoid[extra.TmpID], grandTotalWithSameTax, tax.Value)
									log.Info("[EXTRA] Tax '%s' Type %s (%d) | from : %d <<TO>> total : %d ", tax.Name, tax.Type, tax.Value, tax.Total, total)
								} else {
									total = int((float32(tax.Value) / float32(100)) * float32(extra.SubTotal-salesVoid[extra.TmpID]))
								}

								if total < 0 {
									log.Error("sales_detail_tax error. total is minus. Total : %d - %d (%s) - subtotal : %d | salesId : %s - detailId : %d", total, tax.Value, tax.Type, order.SubTotal, sales.NoNota, detailId)
									total = 0
								}
								if total > 0 {
									_, err = db.Insert("sales_detail_tax", map[string]any{
										"tax_fkid":          tax.ID,
										"total":             total,
										"sales_detail_fkid": extraId,
										"category":          tax.Category,
									})
								}
							}

							if sales.Discount.DiscountNominal > 0 && extra.Product.Discount == "on" {
								disc := float32(0)
								if sales.Discount.DiscountType == "nominal" {
									if jmlHrgTotalDisc == 0 {
										log.Info("jmlHrgTotalDisc is %d, set to : %d", jmlHrgTotalDisc, extra.SubTotal)
										jmlHrgTotalDisc = order.SubTotal
									}
									log.Info("[DISC-EXTRA] (%d - %d) / %d * %d", extra.SubTotal, salesVoid[extra.TmpID], jmlHrgTotalDisc, sales.Discount.DiscountNominal)
									disc = (float32(extra.SubTotal-extra.Discount.DiscountNominal-salesVoid[extra.TmpID]) / float32(jmlHrgTotalDisc)) * float32(sales.Discount.DiscountNominal)
								} else {
									disc = (float32(sales.Discount.Discount) / float32(100)) * float32(extra.SubTotal-extra.Discount.DiscountNominal-salesVoid[extra.TmpID])
									log.Info("[DISC-EXTRA] (%d / 100) * (%d - %d) = %d", sales.Discount.Discount, extra.SubTotal, salesVoid[extra.TmpID], disc)
								}

								if disc > 0 {
									_, err = db.Insert("sales_detail_discount", map[string]any{
										"sales_detail_fkid": extraId,
										"type":              "discount",
										"total":             disc,
									})
								}
							}

							if sales.Discount.VoucherNominal > 0 && extra.Product.Voucher == "on" {
								vouch := float32(0)
								if sales.Discount.VoucherType == "nominal" {
									if jmlHrgTotalVouc == 0 {
										log.Info("jmlHrgTotalVouc is %d, set to : %d", jmlHrgTotalVouc, extra.SubTotal)
										jmlHrgTotalVouc = order.SubTotal
									}
									log.Info("[VOUCH-EXTRA] (%d - %d) / %d * %d", extra.SubTotal, salesVoid[order.TmpID], jmlHrgTotalVouc, sales.Discount.VoucherNominal)
									vouch = float32(extra.SubTotal-extra.Discount.VoucherNominal-salesVoid[extra.TmpID]) / float32(jmlHrgTotalVouc) * float32(sales.Discount.VoucherNominal)
								} else {
									vouch = (float32(sales.Discount.Voucher) / float32(100)) * float32(extra.SubTotal-extra.Discount.VoucherNominal-salesVoid[extra.TmpID])
									log.Info("[VOUCH-EXTRA] (%d / 100) * (%d - %d) = %d", sales.Discount.Voucher, extra.SubTotal, salesVoid[extra.TmpID], vouch)
								}

								if vouch > 0 {
									_, _ = db.Insert("sales_detail_discount", map[string]any{
										"sales_detail_fkid": extraId,
										"type":              "voucher",
										"total":             vouch,
									})
								}
							}
						}
					}

					//insert into member
					if order.Product.CatalogueType == "member" && order.Member.MemberID != "" {
						_, err = db.Insert("members", map[string]any{
							"member_id":     order.Member.MemberID,
							"product_fkid":  order.Product.ProductID,
							"email":         order.Member.Email,
							"name":          order.Member.Name,
							"phone":         order.Member.Phone,
							"date_of_birth": order.Member.DateOfBirth,
							"address":       order.Member.Address,
							"register_date": order.Member.MemberID,
							"expired_date":  time.Now().AddDate(1, 0, 0).Unix() * 1000,
							"admin_fkid":    adminId,
						})
						log.IfError(err)
					}

					for _, promotion := range sales.Promotions {
						total := float32(promotion.PromoNominal)
						if total == 0 {
							total = float32(utils.ToInt(promotion.Value))
						}

						if total > 0 {
							isShouldAdd := false
							//if the product list is not empty, the promotion is applied for specific product
							if len(promotion.PromotionDetail.Products) > 0 && promotion.PromotionDetail.Products[0].ProductDetailFkid != 0 {
								subtotalByProductAll := 0
								for _, product := range promotion.PromotionDetail.Products {
									subtotalByProductAll += subtotalByProduct[product.ProductDetailFkid]
									//warn if productDetailId is 0
									if product.ProductDetailFkid == 0 {
										log.Error("productDetailId is 0! promotion: %v", utils.SimplyToJson(promotion))
									}
								}

								for _, product := range promotion.PromotionDetail.Products {
									if product.ProductDetailFkid == order.Product.ProductDetailID {
										//total = float32(order.SubTotal-salesVoid[order.TmpID]) / float32(subtotalByProduct[order.Product.ProductDetailID]) * total
										total = float32(order.SubTotal-salesVoid[order.TmpID]) / float32(subtotalByProductAll) * total
										log.Info("sales_detail_discount (promotion): %v | %v-%v / %v * (%v or %v)", total, order.SubTotal, salesVoid[order.TmpID], subtotalByProductAll, promotion.PromoNominal, promotion.Value)
										isShouldAdd = true
										break
									}
								}
							} else {
								//products is empty, it's mean promotion is applied to all product
								isShouldAdd = true
								total = float32(order.SubTotal-salesVoid[order.TmpID]) / float32(subtotalAll) * total
							}

							if isShouldAdd && total > 0 {
								log.Info("sales_detail_discount promotion total : %v", total)
								db.Insert("sales_detail_discount", map[string]any{
									"sales_detail_fkid":    detailId,
									"type":                 "promotion",
									"total":                total,
									"sales_promotion_fkid": salesPromotionFkids[promotion.PromotionId],
								})
							}
						}
					}

					//insert sales promotion (detail)
					if order.Promotion.PromotionTypeFkid > 0 {
						//for promo voucher: if promoId is not sent by client, then look for it
						if order.Promotion.PromotionId == 0 {
							order.Promotion.PromotionId = getPromotionId(sales.Promotions[0], adminId)
						}

						_, err = db.Insert("sales_detail_promotion", map[string]any{
							"sales_detail_fkid": detailId,
							"sales_fkid":        sales.NoNota,
							"promotion_value":   promotionValue,
							"promotion_fkid":    order.Promotion.PromotionId,
						})
					}
				}
			}

			if warning != "" {
				log.Error("ERROR HAPPEN! sales '%s' status '%s' user %s(%s) -> %s", sales.NoNota, sales.Status, adminId, sales.OutletID, warning)
			}

			for _, payment := range sales.Payments {
				if payment.Total == 0 && sales.GrandTotal > 0 {
					log.Info("skipp >> payment: %v", payment)
					continue
				}
				res, _ := db.Insert("sales_payment", map[string]any{
					"sales_fkid":   sales.NoNota,
					"method":       payment.Method,
					"total":        payment.Total,
					"pay":          payment.Pay,
					"info":         strings.TrimSpace(payment.Info),
					"time_created": sales.TimeCreated,
				})

				paymentId, _ := res.LastInsertId()

				if payment.Bank.BankID != 0 {
					_, err = db.Insert("sales_payment_bank", map[string]any{
						"sales_payment_fkid": paymentId,
						"bank_fkid":          payment.Bank.BankID,
						// "account_number":     fmt.Sprintf("%+q", utils.TakeMax(utils.RemoveAllEmoji(payment.Bank.AccountNumber), 50)),
						"account_number": utils.TakeMax(utils.RemoveAllEmoji(payment.Bank.AccountNumber), 50),
					})
				}

				//insert to piutang
				total := payment.Pay
				if total < 0 {
					total *= -1
				}

				if payment.Method == "PIUTANG" {
					data := map[string]any{
						"sales_fkid":    sales.NoNota,
						"due_date":      payment.DueDate,
						"info":          strings.TrimSpace(payment.Info),
						"total":         total,
						"unpaid":        total,
						"data_modified": time.Now().Unix() * 1000,
					}
					res, err := db.Insert("piutang", data)
					log.IfError(err)

					id, _ := res.LastInsertId()
					responseData["piutang_id"] = id

					shouldUpdatePoint = false
				} else if payment.Method == "COMPLIMENT" || payment.Method == "DUTY MEALS" {
					shouldUpdatePoint = false
				}
			}

			for _, tax := range sales.Taxes {
				_, err = db.Insert("sales_tax", map[string]any{
					"tax_fkid":   tax.ID,
					"total":      tax.Total,
					"sales_fkid": sales.NoNota,
					"category":   tax.Category,
				})
			}

			//insert promotion
			//log.Info("promotion : %s", utils.SimplyToJson(sales.Promotions))
			log.Info("size of promotion : %d", len(sales.Promotions))
			for _, promotion := range sales.Promotions {
				salesPromotion := map[string]any{
					"sales_fkid": sales.NoNota,
				}
				if promotion.Code != "" {
					if utils.IsNumber(promotion.Code) && promotion.TypeId == 15 {
						salesPromotion["promotion_buy_fkid"] = sales.Promotions[0].Code
						promoId, err := getPromotionIdByPromoBuyId(sales.Promotions[0].Code, adminId)
						if err == nil {
							if promotion.PromotionId > 0 && promoId != promotion.PromotionId {
								log.IfError(fmt.Errorf("promoId doesn't match, %v should be %v -- salesId: %v", promotion.PromotionId, promoId, sales.NoNota))
							}
							promotion.PromotionId = promoId
						}
					} else {
						salesPromotion["voucher_code"] = promotion.Code
					}
				}
				if promotion.PromoNominal > 0 {
					salesPromotion["promotion_value"] = utils.ToInt(promotion.PromoNominal)
				} else {
					salesPromotion["promotion_value"] = utils.ToInt(promotion.Value)
				}

				if promotion.PromotionId > 0 {
					salesPromotion["promotion_fkid"] = promotion.PromotionId
				} else {
					salesPromotion["promotion_fkid"] = getPromotionId(promotion, adminId)
				}

				resp, _ := db.Insert("sales_promotion", salesPromotion)
				id, _ := resp.LastInsertId()
				salesPromotionFkids[promotion.PromotionId] = id
			}

			for _, customField := range sales.CustomFields {
				_, err = db.Insert("sales_custom_values", map[string]any{
					"sales_fkid":               sales.NoNota,
					"setting_transaction_fkid": customField.SettingTransactionFkid,
					"label":                    customField.Label,
					"value":                    customField.Value,
				})
			}

			return nil
		})

		//delete salesId
		delete(syncSalesProcess, sales.NoNota)

		//if os.Getenv("server") != "development" && sales.OutletID != 308 && sales.OutletID != 263 {
		//	if log.IfErrorSetStatus(ctx, err) {
		//		_ = json.NewEncoder(ctx).Encode(response)
		//		return
		//	}
		//}

		log.Info("log sales '%s': %s", sales.NoNota, strings.Join(logSales, " | "))
		log.Info("saving of '%s' took: %v", sales.NoNota, time.Since(timeStart))
		//check the error,
		//if error is because of duplicate, no need to send report
		if err != nil {
			//log.Info("error type : '%v'", reflect.TypeOf(err))
			errCode := utils.ToInt(utils.GetErrCodeSQL(err.Error()))
			if errCode == utils.ERR_SQL_DUPLICATE_PRIMARY { //1062: duplicate primary key
				response.Status = true
				go setCartPaid(sales.NoNota)
				return response, nil
			}
			//else if errCode == utils.ERR_SQL_LOCK_TIMEOUT || errCode == utils.ERR_SQL_DEADLOCK { //1205: lock wait timeout, 1213: deadlock
			//	log.Warn("err on saving sales: '%s' --> %v", sales.NoNota, err)
			//	response.Status = false //set false, we want client re-send the data in the future
			//	return response, nil
			//}

			if driverErr, ok := err.(*mysql.MySQLError); ok {
				log.Info("driver error number : %v", driverErr.Number)
				r, _ := regexp.Compile("^Error 1062: Duplicate entry '[a-zA-Z0-9]{11,15}' for key 'PRIMARY'")
				if driverErr.Number == 1062 && r.MatchString(err.Error()) {
					log.Warn("sales '%s' already in DB, skip", sales.NoNota)
					response.Status = true
					return response, nil
				} else {
					log.Info("driver is not 1062 or regex not match with err msg : '%s'", err.Error())
					return response, err
				}
			} else if err.Error() == fmt.Sprintf("Error 1062: Duplicate entry '%s' for key 'PRIMARY'", sales.NoNota) {
				log.Warn("sales '%s' already in DB, skip", sales.NoNota)
				response.Status = true
				return response, nil
			} else if strings.Contains(err.Error(), fmt.Sprintf("Duplicate entry '%s' for key 'PRIMARY'", sales.NoNota)) {
				log.Warn("sales '%s' already in DB, skip", sales.NoNota)
				response.Status = true
				return response, nil
			} else {
				log.Info("failed saving sales with id: %s", sales.NoNota)
				//if log.IfErrorSetStatus(ctx, err) {
				//	_ = json.NewEncoder(ctx).Encode(response)
				//	return
				//}
				// if os.Getenv("ENV") == "development" && strings.Contains(err.Error(), "Error 1452 (23000): Cannot add or update a child row") {
				// 	log.Info("skip error 1452, in development")
				// 	response.Status = true
				// 	return response, nil
				// }

				response.Message = err.Error()
				return response, err
			}

			//else if err.Error() == "Error 1205: Lock wait timeout exceeded; try restarting transaction" {
			//	log.Warn("Lock wait timeout exceeded; for sales '%s'", sales.NoNota)
			//	response.Status = false
			//	return response, nil
			//} else if err.Error() == "Error 1213: Deadlock found when trying to get lock; try restarting transaction" {
			//	log.Warn("Deadlock found; for sales '%s'", sales.NoNota)
			//	response.Status = false
			//	return response, nil
			//}
		}

		//publish to pubsub
		go publishToSubscriber(sales, string(adminId))

		log.Info("'%s' successfully inserted", sales.NoNota)
		response.Message = "Insert to sale success. "

		if sales.ReceiptReceiver != "" {
			go SendReceipt(sales)
		}

		go calculateCommission(sales)

		isMustSendToBehave := sales.MemberDetail.Source == "behave"
		//fmt.Println("Promotion ", sales.Promotions, " - Count : ", len(sales.Promotions))
		if utils.IsAllowedToIntegrateWithBehave() {
			for _, promotion := range sales.Promotions {
				//if contains promotion from behave, then we must send transaction to Behave server
				if promotion.Source == "behave" {
					isMustSendToBehave = true

					_, err = db.Insert("sales_promotion_outsource", map[string]any{
						"sales_fkid": sales.NoNota,
						"source":     "behave",
					})
					log.IfError(err)
				}
			}
		}

		//if UNIQ Member, check if member can get point
		//fmt.Printf("member source - '%s' (%s)", sales.MemberDetail.Source, sales.MemberId)
		if sales.MemberId != "" && sales.MemberDetail.Source != "behave" {
			go pushNotification("Transaksi Berhasil", "Anda melakukan transaksi senilai "+utils.CurrencyFormat(sales.GrandTotal), sales.MemberId, string(adminId), "sales", utils.SimplyToJson(map[string]any{
				"sales_id": sales.NoNota,
			}))
			if shouldUpdatePoint {
				go CheckPointEarned(sales, string(adminId))
				go CheckMemberLevel(sales.MemberId, string(adminId))
				go updateMemberSpend(sales, cast.ToInt(adminId))
				go func() {

				}()
			}
		}

		if isMustSendToBehave && utils.IsAllowedToIntegrateWithBehave() {
			if sales.MemberDetail.Source != "behave" {
				sales.MemberId = "" // set to empty, in order to not send to behave server
			}
			log.Info("send transaction to behave")
			go behave.PushTransactionToBehave(sales)
		}
		go checkAnomaly(sales)
	}

	if strings.ToLower(sales.Status) == "refund" {
		go removePoint(sales.NoNota, utils.ToString(adminId))
		go publishToSubscriber(sales, string(adminId))
		if len(sales.Promotions) > 0 && sales.Promotions[0].Source != "behave" {
			go refundVoucher(sales.NoNota)
		}
		if utils.IsAllowedToIntegrateWithBehave() {
			go refundTransactionToBehave(sales)
		}
	}

	go setCartPaid(sales.NoNota)

	response.Status = true
	response.Data = responseData
	return response, nil
}

func setCartPaid(salesId string) {
	data := map[string]any{
		"status":           "paid",
		"time_modified":    utils.CurrentMillis(),
		"device_edit_mode": 0,
	}

	res, err := db.UpdateDb("tmp_sales", data, map[string]any{"no_nota": salesId})
	if !log.IfError(err) {
		rows, _ := res.RowsAffected()
		log.Info("update tmpSales effected: %v, id: %v", rows, salesId)
	}
}

func getPromotionIdByPromoBuyId(promotionBuyId, adminId string) (int, error) {
	sql := `
	SELECT
	promotion_fkid
FROM
	promotion_buy pb
	JOIN promotions p ON p.promotion_id = pb.promotion_fkid
WHERE
	promotion_buy_id = ?
	AND p.admin_fkid = ? `
	data, err := db.Query(sql, promotionBuyId, adminId)
	if log.IfError(err) {
		return 0, err
	}

	return utils.ToInt(data["promotion_fkid"]), nil
}

func calculateCommission(sales models.Sale) {
	logs := make([]string, 0)
	defer fmt.Println(strings.Join(logs, " | "))

	sql := `
select sd.sales_detail_id,
       sd.product_detail_fkid,
       (sd.qty - coalesce(sv.qty_void, 0))            as qty_sales,
       (sd.sub_total - coalesce(sv.subtotal_void, 0)) as subtotal_sales
from sales_detail sd
         left join (select sum(abs(qty)) as qty_void, sum(abs(sub_total)) as subtotal_void, sales_detail_fkid
                    from sales_void
                    where sales_fkid = ?
                    group by sales_detail_fkid) sv
                   on sv.sales_detail_fkid = sd.sales_detail_id
 where sd.sales_fkid = ?`
	salesDetail, err := db.QueryArray(sql, sales.NoNota, sales.NoNota)
	log.IfError(err)

	sql = `select commission_customer, commission_customer_type, commission_staff, commission_staff_type, product_detail_id
from products_detail
where product_detail_id in (select product_detail_fkid from sales_detail where sales_fkid = ?)
  and (commission_staff + commission_customer) > 0 `
	commissions, err := db.QueryArray(sql, sales.NoNota)
	log.IfError(err)

	//if no commission applied, then skip..
	logs = append(logs, fmt.Sprintf("[commission] total product with commission: %d", len(commissions)))
	if len(commissions) == 0 {
		return
	}

	commissionMap := make(map[int]map[string]any)
	for _, commission := range commissions {
		commissionMap[utils.ToInt(commission["product_detail_id"])] = commission
	}

	for _, detail := range salesDetail {
		totalCommissionCustomer := 0
		totalCommissionStaff := 0
		if commission, ok := commissionMap[utils.ToInt(detail["product_detail_fkid"])]; ok {
			logs = append(logs, fmt.Sprintf("commission : %v", commission))
			totalCommissionCustomer = utils.ToInt(commission["commission_customer"]) * utils.ToInt(detail["qty_sales"])
			if commission["commission_customer_type"] == "percent" {
				totalCommissionCustomer = int(utils.ToFloat(detail["subtotal_sales"]) / float64(100) * utils.ToFloat(commission["commission_customer"]))
			}

			totalCommissionStaff = utils.ToInt(commission["commission_staff"]) * utils.ToInt(detail["qty_sales"])
			if commission["commission_staff_type"] == "percent" {
				totalCommissionStaff = int(utils.ToFloat(detail["subtotal_sales"]) / float64(100) * utils.ToFloat(commission["commission_staff"]))
			}
		}

		logs = append(logs, fmt.Sprintf("commission customer: %d - staff : %d | qtySales: %v - salesDetailId: %d", totalCommissionStaff, totalCommissionCustomer, detail["qty_sales"], detail["sales_detail_id"]))

		if totalCommissionStaff+totalCommissionCustomer > 0 {
			_, err = db.Update("sales_detail", map[string]any{
				"commission_staff":    totalCommissionStaff,
				"commission_customer": totalCommissionCustomer,
			}, "sales_detail_id = ?", detail["sales_detail_id"])
			log.IfError(err)
		}
	}
}

func getPromotionId(promotion models.Promotion, adminId any) int {
	if promotion.TypeId == 15 {
		promo, err := db.Query("select promotion_fkid from promotion_buy where promotion_buy_id = ?", promotion.Code)
		log.IfError(err)
		return utils.ToInt(promo["promotion_fkid"])
	} else if promotion.TypeId >= 11 && promotion.TypeId <= 13 {
		promo, err := db.Query("select promotion_id from promotion_voucher_code where voucher_code = ? and admin_fkid = ?", promotion.Code, adminId)
		log.IfError(err)
		return utils.ToInt(promo["promotion_id"])
	}

	log.Warn("can not get promotion id...")
	return 0
}

func refundVoucher(salesId string) {
	log.Info("removing voucher for sales '%s'...", salesId)
	promo, err := db.Query("select voucher_code, promotion_buy_fkid, promotion_fkid from sales_promotion where sales_fkid = ?", salesId)

	if log.IfError(err) {
		return
	}

	if len(promo) == 0 {
		log.Warn("sales promo with id '%s' not found!", salesId)
		return
	}

	if promo["promotion_buy_fkid"] != nil {
		log.Info("updating promotion_buy with id '%v'", promo["promotion_buy_fkid"])
		_, err = db.Update("promotion_buy", map[string]any{
			"status": "available",
		}, "promotion_buy_id = ?", promo["promotion_buy_fkid"])
		log.IfError(err)
	} else if promo["voucher_code"] != nil {
		_, err = db.GetDb().Exec("update promotion_voucher_code set used = used-1 where voucher_code = ? and promotion_id = ?", promo["voucher_code"], promo["promotion_fkid"])
		log.IfError(err)
	}

}

func publishToSubscriber(sales models.Sale, adminId string) {
	if sales.NoNota == "" {
		log.IfError(errors.New("can not send pubsub with empty salesId"))
		return
	}

	client := google.GetPubSubClient()

	if client == nil {
		log.Info("can not publish to pubsub, client is nill, salesId %v", sales.NoNota)
		return
	}

	data := map[string]any{
		"sales_id":  sales.NoNota,
		"status":    sales.Status,
		"outlet_id": sales.OutletID,
		"admin_id":  utils.ToInt(adminId),
	}

	env := os.Getenv("server")
	if env == "demo" {
		env = "staging"
	}
	topicId := fmt.Sprintf("pos-sales-%s", env)
	t := client.Topic(topicId)

	ctx := context.Background()
	result := t.Publish(ctx, &pubsub.Message{
		Data: []byte(utils.SimplyToJson(data)),
	})

	id, err := result.Get(ctx)
	if log.IfError(err) {
		// Error handling code can be added here.
		log.Info("failed to publish to topic '%s' : %v", topicId, err)
	}

	log.Info("sales '%s' published to topic: '%s', msg ID: %v\n", sales.NoNota, topicId, id)
}

func removePoint(salesId, adminId string) {
	sql := `select member_fkid, point_earned, o.name, grand_total
from sales s
join outlets o on s.outlet_fkid = o.outlet_id
where sales_id = ?
  and member_fkid is not null`
	sales, err := db.Query(sql, salesId)
	if log.IfError(err) {
		return
	}

	//if no data found, it might be sales not using member id
	if len(sales) == 0 {
		log.Info("[remove point] no data sales found or sales not attach to member!")
		return
	}

	//if point from sales is zero, no need to update point, check level only
	if utils.ToInt(sales["point_earned"]) <= 0 {
		log.Info("[remove point] point earned from sales '%s' is %v. no need to update point", salesId, sales["point_earned"])
		CheckMemberLevel(utils.ToString(sales["member_fkid"]), adminId)
		return
	}

	salesModel := models.Sale{
		Status:     "refund",
		GrandTotal: utils.ToInt(sales["grand_total"]),
		OutletName: utils.ToString(sales["name"]),
		MemberId:   utils.ToString(sales["member_fkid"]),
		NoNota:     salesId,
	}

	log.Info("point removed, memberId: %v, salesId: %v, point: %v", sales["member_fkid"], salesId, sales["point_earned"])
	updatePoint(salesModel, utils.ToInt(sales["point_earned"]), adminId, "")
	CheckMemberLevel(utils.ToString(sales["member_fkid"]), adminId)
}

func CheckMemberLevel(memberId, adminId string) {
	//get current member level (member type)
	memberDetail, err := db.Query("SELECT type_fkid, total_point FROM members_detail WHERE member_fkid = ? and admin_fkid = ? ", memberId, adminId)
	if log.IfError(err) || len(memberDetail) == 0 {
		return
	}

	log.Info("checking member level: %s (%s)", memberId, adminId)
	time.Sleep(5 * time.Second)

	//get current spend and point,
	//query ini mengambil semua point, belum di kurangi dengan yang sudah di belikan utk promo
	//	sql := `select sum(grand_total) as total, sum(point_earned) as total_point
	//from sales s
	//         join outlets o on s.outlet_fkid = o.outlet_id
	//where member_fkid = ?
	//  and status = 'success'
	//  and (payment not like '%duty meal%' or payment not like '%compliment%')
	//  and o.admin_fkid = ?`

	//current_point : current point belong to member (display in app), if point used to buy deals, it will subtracted
	//total point : all point earned by member
	sql := `select coalesce(sum(grand_total), 0) as total_spend, min(md.total_point) as current_point, coalesce(sum(point_earned), 0) as total_point
from members_detail md
         join members m on md.member_fkid = m.member_id
         left join (select grand_total, member_fkid, point_earned
                    from sales s
                             join outlets o on s.outlet_fkid = o.outlet_id
                    where status = 'success'
                      and (payment not like '%duty meal%' or payment not like '%compliment%')
                      and o.admin_fkid = ?) sales on sales.member_fkid = md.member_fkid
where md.member_fkid = ?
  and md.admin_fkid = ? `
	spend, err := db.Query(sql, adminId, memberId, adminId)
	if log.IfError(err) {
		return
	}

	sql = `select sum(point_lost) as point_lost, sum(spend_lost) as spend_lost
from members_type_history
where change_log = 'lifetime'
  and admin_fkid = ?
  and member_fkid = ? `
	dataLost, err := db.Query(sql, adminId, memberId)

	spendLost := utils.ToInt(spend["total_spend"]) - utils.ToInt(dataLost["spend_lost"])
	pointLost := utils.ToInt(spend["total_point"]) - utils.ToInt(dataLost["point_lost"])

	//get member level ordering by point and spend
	sql = `select mt.*, p.name, (point_target+spent_target) as spend_total
from members_type mt
         join products p on mt.product_fkid = p.product_id
where mt.admin_fkid = ?
  and p.data_status = 'on'
 and (spent_target <= ?
  or point_target <= ? )
order by spend_total desc
limit 1 `

	memberType, err := db.Query(sql, adminId, spendLost, pointLost)
	if log.IfError(err) {
		return
	}

	log.Info("current member type : %v | should be %v (%s) | total spend : %d | current point : %d | total point : %d", memberDetail["type_fkid"], memberType["type_id"], memberType["name"], spendLost, memberDetail["total_point"], pointLost)
	if memberType["type_id"] != memberDetail["type_fkid"] {
		log.Info("update member %s level from type %d to %d", memberId, memberDetail["type_fkid"], memberType["type_id"])

		//decide, it's upgrade or downgrade
		isUpgrade := true
		//1st way : if this new level is already in history, consider it as downgrade
		//historyLevel, err := db.Query("select point, spend from members_type_history where member_fkid = ? and admin_fkid = ? and member_type_fkid = ?", memberId, adminId, memberType["type_id"])
		//log.IfError(err)
		//if len(historyLevel) > 0 {
		//	isUpgrade = false
		//}

		//2nd way : compare point and spend in new level, with current
		currentLevel, err := db.Query("select point_target,spent_target, (point_target+spent_target) as spend_total  from members_type where type_id = ? and admin_fkid =? limit 1", memberDetail["type_fkid"], adminId)
		if !log.IfError(err) {
			if utils.ToInt(memberType["spend_total"]) < utils.ToInt(currentLevel["spend_total"]) {
				log.Info("[DOWNGRADE] new level spend total : %v | current level total : %v", memberType["spend_total"], currentLevel["spend_total"])
				isUpgrade = false
			}
		}

		_, err = db.Update("members_detail", map[string]any{
			"type_fkid": memberType["type_id"],
		}, "member_fkid = ? and admin_fkid = ?", memberId, adminId)
		if !log.IfError(err) {
			changeType := "up"
			if !isUpgrade {
				changeType = "down"
			}

			_, err = db.Insert("members_type_history", map[string]any{
				"member_fkid":       memberId,
				"admin_fkid":        adminId,
				"member_type_fkid":  memberType["type_id"],
				"point":             spend["total_point"], //current point
				"spend":             spend["total_spend"], //current spend
				"member_type_point": memberType["point_target"],
				"member_type_spend": memberType["spent_target"],
				"change_type":       changeType,
				"data_created":      time.Now().Unix() * 1000,
			})
			log.IfError(err)

			msg := fmt.Sprintf("Horreee! Sekarang kamu naik level menjadi %s. Terus lakukan transaksi untuk naik level lagi", memberType["name"])
			title := "Member Level Upgraded"
			if !isUpgrade {
				msg = fmt.Sprintf("Sorry, kamu harus turun level menjadi %s. Terus lakukan transaksi agar kamu bisa naik level lagi", memberType["name"])
				title = "Member Level Downgraded"
			}
			pushNotification(title, msg, memberId, adminId, nil, nil)
		}
	}
}

func checkAnomaly(sales models.Sale) {
	//time.Sleep(5 * time.Second)
	//for _, order := range sales.OrderList {
	//	data, err := db.QueryArray("select no_nota from tmp_sales where outlet_fkid = ? and status='pending' and sales like ?", sales.OutletID, fmt.Sprintf("%%%d%%", order.TmpID))
	//	if !log.IfError(err) && len(data) > 0 {
	//		log.Error(fmt.Sprintf("WARNING. Error might be happen for salesId : '%s' - tmpSalesId : '%s' - outlet : %s(%d)", sales.NoNota, data[0]["no_nota"], sales.OutletName, sales.OutletID))
	//		break
	//	}
	//}
	fmt.Println(sales.Table)
}

func refundTransactionToBehave(sales models.Sale) {
	//check if transaction use any promo from behave, if yes, submit refund to behave server
	count, err := db.Query("SELECT sales_promo_id, info FROM sales_promo WHERE sales_fkid = ? and source = 'behave' ", sales.NoNota)
	utils.CheckErr(err)

	isMustSendToBehave := len(count) > 0

	if !isMustSendToBehave {
		count, err = db.Query("select member_behave_id from member_behave mb join sales s on s.member_fkid=mb.member_fkid where sales_id = ? ", sales.NoNota)
		utils.CheckErr(err)
		isMustSendToBehave = len(count) > 0
	}

	if isMustSendToBehave {
		fmt.Println("Push refund to behave..")
		behave.PushRefundTransaction(sales)
	}
}

func CheckPointEarned(sale models.Sale, adminId string) {
	timeOffset := 25200

	//check level
	member, err := db.Query("SELECT type_fkid FROM members_detail WHERE member_fkid = ? AND admin_fkid = ? LIMIT 1", sale.MemberId, adminId)
	if log.IfError(err) {
		return
	}

	log.Info("member : %s", utils.SimplyToJson(member))
	if len(member) == 0 {
		log.Info("member type not found!")
		return
	}

	//check active point collection
	dayList := []string{"day_sunday", "day_monday", "day_tuesday", "day_wednesday", "day_thursday", "day_friday", "day_saturday"}
	day := dayList[int(time.Now().Weekday())]
	//timeNow := time.Now().Unix() * 1000

	sql := `
select point_collection_id, min_transaction
from point_collection pc
         left join point_collection_detail_nominal pcdn on pc.point_collection_id = pcdn.point_collection_fkid
         left join point_collection_detail_product pcdp on pc.point_collection_id = pcdp.point_collection_fkid
where member_type_fkid = $member_type
  and min_transaction <= $grand_total
  and CONVERT(from_unixtime(date_start / 1000 + $offset, '%Y-%m-%d'), DATE) <= CONVERT(from_unixtime($time / 1000 + $offset, '%Y-%m-%d'), DATE)
  and CONVERT(from_unixtime(date_end / 1000 + $offset, '%Y-%m-%d'), DATE) >= CONVERT(from_unixtime($time / 1000 + $offset, '%Y-%m-%d'), DATE)
  and time_start <= (select from_unixtime(($time / 1000) + $offset, '%T'))
  and time_end >= (select from_unixtime(($time / 1000) + $offset, '%T'))
  and coalesce(pcdp.outlet_fkid, pcdn.outlet_fkid) = $outlet_id
  and $day = 1 `

	sql = strings.Replace(sql, "$day", day, -1)
	//point, err := db.Query(sql, member["type_fkid"], sale.GrandTotal, sale.TimeCreated, sale.TimeCreated, sale.TimeCreated, timeOffset,
	//	sale.TimeCreated, timeOffset, sale.OutletID)

	points, err := db.QueryArrayFun(sql, map[string]any{
		"member_type": member["type_fkid"],
		"grand_total": sale.GrandTotal,
		"outlet_id":   sale.OutletID,
		"time":        sale.TimeCreated,
		"offset":      timeOffset,
	})

	if log.IfError(err) {
		return
	}

	if len(points) == 0 {
		log.Info("no point collection found!")
		return
	}

	log.Debug("point collection available : %d", len(points))

	for _, point := range points {
		//check in nominal
		sql = `select point, transaction, repeat_point
from point_collection_detail_nominal
where point_collection_fkid = ?
  and outlet_fkid = ?
  and is_active = 1
LIMIT 1 `
		pointNominal, err := db.Query(sql, point["point_collection_id"], sale.OutletID)
		log.IfError(err)

		log.Info("point nominal : %s", utils.SimplyToJson(pointNominal))
		if len(pointNominal) > 0 {
			pointTotal := 0
			transaction := utils.ToInt(pointNominal["transaction"])
			if transaction == 0 {
				transaction = utils.ToInt(point["min_transaction"])
			}
			if utils.ToInt(pointNominal["repeat_point"]) == 1 {
				log.Info("[REPEAT] %d * (%d/%d)", pointNominal["point"], sale.GrandTotal, transaction)
				pointTotal = utils.ToInt(pointNominal["point"]) * (sale.GrandTotal / transaction)
			} else {
				pointTotal = utils.ToInt(pointNominal["point"])
			}

			log.Info("total point gained from nominal : %d", pointTotal)
			if pointTotal > 0 {
				updatePoint(sale, pointTotal, adminId, "nominal")
				break
			}
		}

		//check point collection for product --------->
		productVariantFormat := "%d,%d"
		productCount := make(map[string]int)
		params := make([]any, 0)
		params = append(params, point["point_collection_id"])
		params = append(params, sale.OutletID)
		totalQty := 0
		for _, order := range sale.OrderList {
			if (order.Discount.DiscountType == "percentage" && order.Discount.Discount == 100) || order.Promotion.PromotionTypeFkid == utils.PROMO_FREE {
				fmt.Printf("--- skip = disc %d (%s) | promo : %d \n", order.Discount.Discount, order.Discount.DiscountType, order.Promotion.PromotionTypeFkid)
				continue
			}

			key := fmt.Sprintf(productVariantFormat, order.Product.ProductID, order.Product.VariantFkid)
			params = append(params, key) //order.Product.ProductID
			productCount[key] += utils.ToAbs(order.Qty, !order.IsItemVoid)
			totalQty += productCount[key]
			log.Info("prod id : %d | variant : %d | qty : %d | isVoid : %v", order.Product.ProductID, order.Product.VariantFkid, order.Qty, order.IsItemVoid)

			for _, extra := range order.Extra {
				key = fmt.Sprintf(productVariantFormat, extra.Product.ProductID, extra.Product.VariantFkid)
				params = append(params, key)
				productCount[key] += utils.ToAbs(extra.Qty, !extra.IsItemVoid)
				totalQty += productCount[key]
			}
		}

		if totalQty <= 0 {
			log.Info("total qty is : %d -- return", totalQty)
			return
		}

		fmt.Println("point collection id", point["point_collection_id"])
		log.Info("product params --> %v", params)
		pointProducts, err := db.QueryArray("select product_fkid, variant_fkid, point, repeat_point from point_collection_detail_product "+
			"where point_collection_fkid = ? and outlet_fkid = ? and is_active = 1 and concat(product_fkid, ',',coalesce(variant_fkid,'0')) IN ("+strings.Repeat("?,", len(params)-2-1)+"?)", params...)
		log.IfError(err)

		log.Info("point collection product found : %d", len(pointProducts))
		pointTotal := 0
		for _, product := range pointProducts {
			key := fmt.Sprintf(productVariantFormat, utils.ToInt(product["product_fkid"]), utils.ToInt(product["variant_fkid"]))
			log.Info("product id : %v | variant : %v | point : %d | repeat : %d | total qty buy : %d", product["product_fkid"], product["variant_fkid"], product["point"], product["repeat_point"], productCount[key])

			if productCount[key] <= 0 {
				log.Info("not calculate... ")
				continue
			}

			if utils.ToInt(product["repeat_point"]) == 1 {
				pointTotal += utils.ToInt(product["point"]) * productCount[key]
			} else {
				pointTotal += utils.ToInt(product["point"])
			}
		}

		log.Info("total point gained from product : %d ", pointTotal)
		if pointTotal > 0 {
			updatePoint(sale, pointTotal, adminId, "product")
			break
		}
	}

}

func updatePoint(sale models.Sale, pointNew int, adminId, earnType string) {
	title := fmt.Sprintf("Kamu dapat %s point", utils.CurrencyFormat(pointNew))
	msg := fmt.Sprintf("Selamat, kamu mendapatkan tambahan point karena sudah berbelanja di %s", sale.OutletName)
	if earnType == "nominal" {
		msg = fmt.Sprintf("Selamat, kamu mendapatkan %d point karena sudah berbelanja sebesar Rp%s di %s", pointNew, utils.CurrencyFormat(sale.GrandTotal), sale.OutletName)
	}

	if strings.ToLower(sale.Status) == "refund" {
		title = "Perubahan Point"
		msg = fmt.Sprintf("Ops, point kamu harus berkurang %d. Transaksi senilai %d di %s telah di refund", pointNew, sale.GrandTotal, sale.OutletName)
		if pointNew > 0 {
			pointNew *= -1
		}
		log.Info("sales status is refund! point minus : %d", pointNew)
	}

	notifTitle, notifMessage := getRandomNotification(NotificationParams{
		TotalPoint:        int(math.Abs(float64(pointNew))),
		GrandTotal:        sale.GrandTotal,
		OutletName:        sale.OutletName,
		EarnType:          earnType,
		TransactionStatus: sale.Status,
	})

	log.Info("notif title: %s | notif message: %s >> original title: %s | original message: %s", notifTitle, notifMessage, title, msg)
	title = notifTitle
	msg = notifMessage

	_, err := db.GetDb().Exec(fmt.Sprintf("update members_detail set total_point = greatest(total_point + %d, 0) where member_fkid = ? and admin_fkid = ? ", pointNew), sale.MemberId, adminId)
	if !log.IfError(err) {
		if strings.ToLower(sale.Status) == "refund" {
			pointNew = 0
		}
		_, err = db.UpdateDb("sales", map[string]any{
			"point_earned": pointNew,
		}, map[string]any{
			"sales_id": sale.NoNota,
		})
		log.IfError(err)

		pushNotification(title, msg, sale.MemberId, adminId, nil, nil)
	}
}

func pushNotification(title, message, memberId, adminId string, notifType, data any) {
	resp, err := db.Insert("system_notification", map[string]any{
		"title":             title,
		"message":           message,
		"type":              "member",
		"receiver_id":       memberId,
		"admin_fkid":        adminId,
		"data_created":      time.Now().Unix() * 1000,
		"notification_type": notifType,
		"notification_data": data,
	})
	if log.IfError(err) {
		return
	}
	notifId, _ := resp.LastInsertId()

	member, err := db.Query("SELECT firebase_token FROM members_detail where member_fkid = ? and admin_fkid = ? LIMIT 1", memberId, adminId)
	if log.IfError(err) {
		return
	}

	if member["firebase_token"] == nil {
		log.Warn("member '%s' (%s) has no token to push notification", memberId, adminId)
		return
	}

	// request := utils.HttpRequest{}
	// request.Method = "POST"
	// request.Url = "https://fcm.googleapis.com/fcm/send"
	// request.Header = map[string]any{
	// 	"Authorization": "key = AIzaSyBzCzuMS1dtkHGi0QDp7W76DKaQR3_3u8k",
	// 	"Content-Type":  "application/json",
	// }
	// request.PostRequest.Body = map[string]any{
	// 	"registration_ids": []string{utils.ToString(member["firebase_token"])},
	// 	"data": map[string]any{
	// 		"message": message,
	// 		"title":   title,
	// 		"id":      notifId,
	// 	},
	// }
	// body, err := request.Execute()
	// log.IfError(err)
	// fmt.Println("FMC Resp : ", string(body))

	err = google.SendPushNotif(google.NotificationData{
		Token: cast.ToString(member["firebase_token"]),
		Data: map[string]string{
			"title":   title,
			"message": message,
			"id":      fmt.Sprintf("%d", notifId),
		},
	}, true)
	log.IfError(err)
}

func generateDynamicLink(appConf models.CrmAppInfo, salesId, shortFeedbackUrl string) string {
	if appConf.Android.PackageName == "" {
		return shortFeedbackUrl
	}
	link := fmt.Sprintf("https://crm-page.uniq.id/feedback/%s?redirect=%s", salesId, shortFeedbackUrl)

	if strings.TrimSpace(appConf.Web.Url) != "" {
		link = fmt.Sprintf("%s/inbox/sales/%s", appConf.Web.Url, salesId)
	}

	log.Info("web dynamic link: %v", link)

	//crate dynamic link
	req := utils.HttpRequest{
		Method: "POST",
		Url:    fmt.Sprintf("https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=%s", os.Getenv("FIREBASE_CRM_KEY")),
		PostRequest: utils.PostRequest{Body: map[string]any{
			"dynamicLinkInfo": map[string]any{
				"domainUriPrefix": utils.FirebaseDynamicLink(),
				"link":            link,
				"androidInfo": map[string]any{
					"androidPackageName": appConf.Android.PackageName,
				},
				"iosInfo": map[string]any{
					"iosBundleId": appConf.Ios.BundleID,
				},
			},
			"suffix": map[string]any{
				"option": "SHORT",
			},
		}},
	}
	res, err := req.Execute()
	log.IfError(err)

	var respMap map[string]any
	if !log.IfError(json.Unmarshal(res, &respMap)) {
		if dynamicLink := cast.ToString(respMap["shortLink"]); dynamicLink != "" {
			log.Info("dynamic link created: %v", dynamicLink)
			return dynamicLink
		} else {
			log.Error("firebase dynamic link doesn't return shortLink. %v", string(res))
		}
	} else {
		fmt.Println("err json from firebase: " + string(res))
	}
	return shortFeedbackUrl
}

// use probability to determine if use custom number (using firebase remote config)
func useCustomNumber() bool {
	config, err := google.GetFirebaseRemoteConfig()
	if log.IfError(err) {
		return false
	}

	prob := cast.ToInt(config.Get("api_receipt_sender"))
	return (rand.Intn(100) + 1) <= prob
}

func getPointCollectionFeedback(adminId, memberId int) models.PointCollectionEntity {
	sql := `SELECT pc.* from members m join members_detail md 
	on m.member_id=md.member_fkid and md.admin_fkid= @adminId
	join point_collection pc on md.type_fkid=pc.member_type_fkid and pc.admin_fkid= @adminId
	where m.member_id = @memberId and pc.point_type='feedback' 
	and pc.date_start < UNIX_TIMESTAMP()*1000 and pc.date_end > UNIX_TIMESTAMP()*1000
	and $day = 1 and time_start < @time and time_end > @time 
	and data_status = 1 and point > 0 
	ORDER BY pc.point_collection_id desc 
	LIMIT 1`

	currentTime := time.Now().Add(7 * time.Hour)
	sql = strings.Replace(sql, "$day", fmt.Sprintf("day_%s", strings.ToLower(currentTime.Weekday().String())), 1)

	sql, param := db.MapParam(sql, map[string]any{
		"adminId":  adminId,
		"memberId": memberId,
		"time":     currentTime.Format("15:04:05"),
	})

	result, err := db.Query(sql, param...)
	if log.IfError(err) {
		return models.PointCollectionEntity{}
	}

	resultJson, _ := json.Marshal(result)

	var pointCollection models.PointCollectionEntity
	err = json.Unmarshal(resultJson, &pointCollection)
	log.IfError(err)
	return pointCollection
}

func receiptTranslation(langId string) map[string]string {
	lng := lang.New(lang.IdFromString(langId))
	langDefault := map[string]string{
		"receipt_num": lng.String(lang.ReceiptNumber),
		"customer":    lng.String(lang.Customer),
		"date":        lng.String(lang.Date),
		"cashier":     lng.String(lang.Cashier),
		"payment":     lng.String(lang.Payment),
		"table":       lng.String(lang.Table),
	}

	return langDefault
}

func getAppCrmConfig(adminId int) models.CrmAppInfo {
	//temporary disable: until mobile/web app fixed
	// if os.Getenv("ENV") == "staging" && adminId == 10 {
	// 	return models.CrmAppInfo{}
	// }

	crmApp, err := db.Query("select * from crm_app where admin_fkid = ? ", adminId)
	if log.IfError(err) || len(crmApp) == 0 {
		return models.CrmAppInfo{}
	}

	var crmAppInfo models.CrmAppInfo
	log.IfError(json.Unmarshal([]byte(utils.ToString(crmApp["app_info"])), &crmAppInfo))
	return crmAppInfo

	// if os.Getenv("server") == "development" {
	// 	//if adminId == 1 {
	// 	//	return "com.uniq.uniqmembership.yamiepanda"
	// 	//}
	// 	return "com.uniq.uniqmembership.yamiepanda"
	// } else if os.Getenv("server") == "production" {
	// 	if adminId == 2 {
	// 		return "com.uniq.uniqmembership.packmarket"
	// 	}
	// }

	// return ""
}

var shouldSyncFromBeginning = true

func GetSalesByOutlet(ctx *fasthttp.RequestCtx) {
	timeStart := time.Now()
	adminId := ctx.Request.Header.Peek("admin_id")
	deviceId := utils.ToString(ctx.Request.Header.Peek("Device"))
	response := models.ResponseArray{Status: true, Data: make([]map[string]any, 0), Millis: (time.Now().Unix() * 1000) - 30000} //minus 30 seconds
	if !auth.ValidateOutlet(ctx) {
		return
	}

	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")
	minAllowToSync := time.Now().AddDate(0, -3, 0).Unix() * 1000

	if utils.ToInt64(lastSync) < minAllowToSync {
		log.Debug("outlet %s (deviceId: %v) request sales longer tan minAllow. minReq : %v | req : %v", outletId, deviceId, minAllowToSync, lastSync)
		lastSync = minAllowToSync
	}

	//to force to get more than 3 months
	if utils.ToInt(outletId) == 290 && utils.ToString(adminId) == "1" && utils.ToString(ctx.Request.Header.Peek("Device")) == "357557061373351" {
		if shouldSyncFromBeginning && utils.ToInt64(lastSync) > 1605069375637 && time.Now().Unix() < 1605074400000/1000 {
			shouldSyncFromBeginning = false
			lastSync = 1593536400000
			log.Error("force last sync to beginning of this year")
		} else {
			lastSync = ctx.UserValue("lastSync")
		}
		log.Info("--> last sync : %s", time.Unix(utils.ToInt64(lastSync)/1000, 0).Format("15:04:05 02/01/2006"))
	}

	//page := ctx.UserValue("page")
	max := 200

	//check time diff betweent last sync and now in seconds
	diffHour := time.Since(time.Unix(utils.ToInt64(lastSync)/1000, 0)).Hours()
	if diffHour <= 3 {
		max = 10
	}

	//page, err := strconv.Atoi(page.(string))
	//startAt := max * page.(int)

	//first, fetching sales_ids
	sql := `SELECT sales_id
	FROM sales s
	WHERE s.outlet_fkid = ?
	  AND s.time_modified > ?
	  AND s.data_status = 'on'
	ORDER BY s.time_modified
	LIMIT ?`
	//.... todo here

	//second, fetch data based on sales_ids above
	// 	sql = `SELECT s.*, o.name as outlet_name, sr.reason
	// FROM sales s
	//          JOIN outlets o ON s.outlet_fkid = o.outlet_id
	//          left join sales_refund sr on s.sales_id = sr.sales_fkid
	// WHERE s.outlet_fkid = ?
	//   AND s.time_modified > ?
	//   AND s.data_status = 'on'
	// ORDER BY s.time_modified
	// LIMIT ? `

	sql = `SELECT s.*, o.name as outlet_name
FROM sales s
         JOIN outlets o ON s.outlet_fkid = o.outlet_id
WHERE s.outlet_fkid = ?
  AND s.time_modified > ?
  AND s.data_status = 'on'
ORDER BY s.time_modified
LIMIT ? `
	sales, err := db.QueryArray(sql, utils.ToInt(outletId), utils.ToInt64(lastSync), max)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	if len(sales) == 0 {
		log.IfError(json.NewEncoder(ctx).Encode(response))
		log.Info("get sales - user : '%s' (%v) | lastSync : %v | took : %v (%d data) | imei : %s", string(adminId), outletId, lastSync, time.Since(timeStart), len(sales), deviceId)
		return
	}

	//get sales refund
	salesIds := make([]string, len(sales))
	for _, row := range sales {
		salesIds = append(salesIds, cast.ToString(row["sales_id"]))
	}
	sql = "select * from sales_refund where sales_fkid in @salesIds"
	sql, params := db.MapParam(sql, map[string]any{
		"salesIds": salesIds,
	})
	salesRefunds, err := db.QueryArray(sql, params...)
	log.IfError(err)

	salesRefundMap := make(map[string]map[string]any)
	for _, refund := range salesRefunds {
		salesRefundMap[cast.ToString(refund["sales_fkid"])] = refund
	}

	//get sales tag
	sql = `SELECT s.sales_id, st.name from sales s 
	JOIN sales_tag st on s.sales_tag_fkid=st.sales_tag_id
	where s.sales_id in @salesIds `
	sql, params = db.MapParam(sql, map[string]any{
		"salesIds": salesIds,
	})

	salesTags, err := db.QueryArray(sql, params...)
	log.IfError(err)

	salesTagMap := make(map[string]any)
	for _, tag := range salesTags {
		salesTagMap[cast.ToString(tag["sales_id"])] = tag
	}

	for i, sale := range sales {
		salesId := cast.ToString(sale["sales_id"])
		if refund, ok := salesRefundMap[salesId]; ok {
			sales[i]["reason"] = refund["reason"]
			sales[i]["sales_refund"] = refund
		}
		if tag, ok := salesTagMap[salesId]; ok {
			sales[i]["sales_tag"] = tag
		}

	}

	log.Info("get sales - user : '%s' (%v) | lastSync : %v | took : %v (%d data) | imei : %s", string(adminId), outletId, lastSync, time.Since(timeStart), len(sales), utils.ToString(ctx.Request.Header.Peek("Device")))

	millis, result := getSalesDetail(sales)
	response.Millis = millis
	response.Data = result

	if time.Since(timeStart) > 20*time.Second {
		log.Info("getSalesWithDetail, tooks %v, millis: %v | size: %v", time.Since(timeStart), millis, len(response.Data))
	}
	_ = json.NewEncoder(ctx).Encode(response)
}

func GetSalesByIds(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Status: true, Millis: time.Now().Unix() * 1000, Message: "success"}
	adminId := ctx.Request.Header.Peek("admin_id")
	salesIdsStr := string(ctx.QueryArgs().Peek("sales_ids"))

	if salesIdsStr == "" {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	salesIds := strings.Split(salesIdsStr, ",")

	sql := "select s.* from sales s join outlets o on o.outlet_id=s.outlet_fkid where o.admin_fkid = ? and s.sales_id in (" + strings.Repeat("?,", len(salesIds)-1) + "?)"
	sales, err := db.QueryArray(sql, utils.ArrayOf(string(adminId), salesIds)...)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	_, result := getSalesDetail(sales)
	response.Data = result
	_ = json.NewEncoder(ctx).Encode(response)
}

func getSalesDetail(sales []map[string]any) (int64, []map[string]any) {
	millis := int64(0)
	saleIds := make([]any, len(sales))
	for i, sale := range sales {
		saleIds[i] = sale["sales_id"]
		millis = sale["time_modified"].(int64)
	}

	var wg sync.WaitGroup
	saleDetailChan := make(chan []map[string]any, 1)
	saleVoidsChan := make(chan []map[string]any, 1)
	saleTaxesChan := make(chan []map[string]any, 1)
	salePaymentsChan := make(chan []map[string]any, 1)
	promotionsOtherChan := make(chan []map[string]any, 1)
	promotionsChan := make(chan []map[string]any, 1)
	promotionsDetailChan := make(chan []map[string]any, 1)

	wg.Add(1)
	sql := `SELECT sd.*,
       p.barcode,
       p.name,
       p.product_subcategory_fkid,
       p.unit_fkid,
       p.sku,
       COALESCE(CONCAT(p.name, ' (', pdv.variant_name, ')'), p.name) AS name,
       (sd.price * sd.qty) - coalesce(sdp.promotion_value, 0)        as subtotal_after_promo
FROM sales_detail sd
         JOIN products_detail pd on sd.product_detail_fkid = pd.product_detail_id
         LEFT JOIN products_detail_variant pdv on pd.variant_fkid = pdv.variant_id
         JOIN products p ON pd.product_fkid = p.product_id
         left join sales_detail_promotion sdp on sdp.sales_detail_fkid = sd.sales_detail_id `
	go db.QueryArrayGo(&wg, saleDetailChan, sql+" WHERE sd.sales_fkid IN ("+strings.Repeat("?,", len(saleIds)-1)+"?)", saleIds...)

	wg.Add(1)
	sql = `SELECT sv.*,
       p.barcode,
       p.name,
       p.product_subcategory_fkid,
       p.unit_fkid,
       p.sku,
       COALESCE(CONCAT(p.name, ' (', pdv.variant_name, ')'), p.name) AS name,
       s.child_type
FROM sales_void sv
         join sales_detail s on sv.sales_detail_fkid = s.sales_detail_id
         JOIN products p ON sv.product_fkid = p.product_id
         JOIN products_detail pd on sv.product_detail_fkid = pd.product_detail_id
         LEFT JOIN products_detail_variant pdv on pd.variant_fkid = pdv.variant_id `
	go db.QueryArrayGo(&wg, saleVoidsChan, sql+" WHERE sv.sales_fkid IN ("+strings.Repeat("?,", len(saleIds)-1)+"?)", saleIds...)

	wg.Add(1)
	go db.QueryArrayGo(&wg, saleTaxesChan, "SELECT st.total, g.name, sales_fkid, tax_category FROM sales_tax st "+
		" JOIN gratuity g ON st.tax_fkid=g.gratuity_id "+
		" WHERE sales_fkid IN ("+strings.Repeat("?,", len(saleIds)-1)+"?)", saleIds...)

	wg.Add(1)
	go db.QueryArrayGo(&wg, salePaymentsChan, "SELECT info, method, pay, payment_id, total,sales_fkid FROM sales_payment  "+
		"WHERE sales_fkid IN ("+strings.Repeat("?,", len(saleIds)-1)+"?)", saleIds...)

	if utils.IsAllowedToIntegrateWithBehave() {
		wg.Add(1)
		go db.QueryArrayGo(&wg, promotionsOtherChan, "select sp.*, sp.promo_value as promotion_value, COALESCE(p.name, sp.info) as name from sales_promo sp "+
			" LEFT JOIN promotion_buy pb on CONVERT(pb.promotion_buy_id, char)=sp.promo_code "+
			" LEFT JOIN promotions p on p.promotion_id=pb.promotion_fkid "+
			" WHERE sales_fkid IN ("+strings.Repeat("?,", len(saleIds)-1)+"?)", saleIds...)
	}

	sql = `select s.sales_id as sales_fkid, sp.promotion_value, p.name, sp.voucher_code, p.promotion_type_id, p.promotion_id
from sales s
         join sales_promotion sp on s.sales_id = sp.sales_fkid
         join promotions p on sp.promotion_fkid = p.promotion_id `
	sql += " where sales_id IN (" + strings.Repeat("?,", len(saleIds)-1) + "?) "

	wg.Add(1)
	go db.QueryArrayGo(&wg, promotionsChan, sql, saleIds...)

	//TODO: remove this query, once the improved query has no bug
	sql = `
select sd.sales_detail_id,
       sv.sales_void_id,
       sdp.promotion_value,
       p.promotion_id,
       p.name,
       p.promotion_type_id,
       p.promotion_type_id                    as promotion_type_fkid,
       coalesce(sd.sales_fkid, sv.sales_fkid) as sales_fkid
from sales_detail_promotion sdp
         left join promotions p on p.promotion_id = sdp.promotion_fkid
         left join sales_detail sd on sd.sales_detail_id = sdp.sales_detail_fkid
         left join sales_void sv on sdp.sales_void_fkid = sv.sales_void_id `
	sql += " where coalesce(sd.sales_fkid, sv.sales_fkid) IN (" + strings.Repeat("?,", len(saleIds)-1) + "?)"

	//improved query
	sql = `select sd.sales_detail_id,
		sdp.sales_void_fkid  as sales_void_id,
		sdp.promotion_value,
		p.promotion_id,
		p.name,
		p.promotion_type_id,
		p.promotion_type_id  as promotion_type_fkid,
		sdp.sales_fkid 
 from sales_detail_promotion sdp
		  left join promotions p on p.promotion_id = sdp.promotion_fkid
		  left join sales_detail sd on sd.sales_detail_id = sdp.sales_detail_fkid `
	sql += " where sdp.sales_fkid IN (" + strings.Repeat("?,", len(saleIds)-1) + "?)"
	wg.Add(1)
	go db.QueryArrayGo(&wg, promotionsDetailChan, sql, saleIds...)

	wg.Wait()

	saleDetails := <-saleDetailChan
	saleVoids := <-saleVoidsChan
	saleTaxes := <-saleTaxesChan
	salePayments := <-salePaymentsChan
	//salePromotions := make([]map[string]any, 0)
	salePromotions := <-promotionsChan
	salesDetailPromotions := <-promotionsDetailChan

	if utils.IsAllowedToIntegrateWithBehave() {
		//promoOthers := <-promotionsOtherChan
		for _, promo := range <-promotionsOtherChan {
			salePromotions = append(salePromotions, promo)
		}
	}

	for _, void := range saleVoids {
		void["is_item_void"] = true
		saleDetails = append(saleDetails, void)
	}

	paymentIds := make([]any, 0)
	for _, payment := range salePayments {
		paymentIds = append(paymentIds, payment["payment_id"])
	}

	if len(paymentIds) > 0 {
		salePaymentBanks, err := db.QueryArray("SELECT pmb.*, spb.account_number, spb.sales_payment_fkid FROM sales_payment_bank spb JOIN payment_media_bank pmb ON spb.bank_fkid=pmb.bank_id "+
			"WHERE sales_payment_fkid IN ("+strings.Repeat("?,", len(paymentIds)-1)+"?)", paymentIds...)
		log.IfError(err)

		for _, payment := range salePayments {
			bank := make(map[string]any, 0)
			for _, paymentBank := range salePaymentBanks {
				if paymentBank["sales_payment_fkid"] == payment["payment_id"] {
					bank = paymentBank
				}
			}
			if len(bank) > 0 {
				payment["bank"] = bank
			}
		}
	}

	salesDetailPromotionMap := make(map[int][]map[string]any)
	salesVoidPromotionMap := make(map[int][]map[string]any)
	for _, promotion := range salesDetailPromotions {
		if promotion["sales_void_id"] != nil {
			promoDetail := salesVoidPromotionMap[utils.ToInt(promotion["sales_void_id"])]
			if promoDetail == nil {
				salesVoidPromotionMap[utils.ToInt(promotion["sales_void_id"])] = make([]map[string]any, 0)
			}
			salesVoidPromotionMap[utils.ToInt(promotion["sales_void_id"])] = append(promoDetail, promotion)
		} else {
			promoDetail := salesDetailPromotionMap[utils.ToInt(promotion["sales_detail_id"])]
			if promoDetail == nil {
				salesDetailPromotionMap[utils.ToInt(promotion["sales_detail_id"])] = make([]map[string]any, 0)
			}
			salesDetailPromotionMap[utils.ToInt(promotion["sales_detail_id"])] = append(promoDetail, promotion)
		}
	}

	promotions := make([]map[string]any, 0)
	for _, promotion := range salePromotions {
		if promotion["info"] == "behave" {
			promotion["source"] = "behave"
		} else {
			promotion["source"] = "uniq"
		}
		if promotion["promotion_value"] != nil {
			promotion["value"] = promotion["promotion_value"]
		}

		promotion["code"] = promotion["promo_code"]
		promotion["type_id"] = promotion["promotion_type_id"]

		promotions = append(promotions, promotion)
	}

	taxMap := array.GroupBy(saleTaxes, "sales_fkid")
	paymentMap := array.GroupBy(salePayments, "sales_fkid")
	promotionMap := array.GroupBy(promotions, "sales_fkid")

	for _, sale := range sales {
		detail := make([]map[string]any, 0)
		// taxes := make([]map[string]any, 0)
		// payments := make([]map[string]any, 0)
		// promotions := make([]map[string]any, 0)
		salesId := cast.ToString(sale["sales_id"])

		for _, salesDetail := range saleDetails {
			if salesDetail["sales_fkid"] == sale["sales_id"] {
				promotion := salesDetailPromotionMap[utils.ToInt(salesDetail["sales_detail_id"])]
				if utils.ToInt(salesDetail["sales_void_id"]) > 0 {
					promotion = salesVoidPromotionMap[utils.ToInt(salesDetail["sales_void_id"])]
				}

				promotionValueTotal := 0
				for _, promo := range promotion {
					promotionValueTotal += utils.ToInt(promo["promotion_value"])
				}

				subTotalAfterPromo := (utils.ToInt(salesDetail["qty"]) * utils.ToInt(salesDetail["price"])) - promotionValueTotal
				qty := utils.ToInt(salesDetail["qty"])
				if qty <= 0 {
					qty = 1
				}
				salesDetail["sub_total"] = subTotalAfterPromo
				salesDetail["price"] = subTotalAfterPromo / qty
				salesDetail["promotions"] = promotion

				detail = append(detail, salesDetail)
			}
		}

		// for _, promotion := range salePromotions {
		// 	if promotion["sales_fkid"] == sale["sales_id"] {
		// 		if promotion["info"] == "behave" {
		// 			promotion["source"] = "behave"
		// 		} else {
		// 			promotion["source"] = "uniq"
		// 		}
		// 		if promotion["promotion_value"] != nil {
		// 			promotion["value"] = promotion["promotion_value"]
		// 		}

		// 		promotion["code"] = promotion["promo_code"]
		// 		promotion["type_id"] = promotion["promotion_type_id"]
		// 		promotions = append(promotions, promotion)
		// 	}
		// }
		sale["detail"] = detail
		sale["tax"] = taxMap[salesId]
		sale["payments"] = paymentMap[salesId]
		sale["promotion"] = promotionMap[salesId] //promotions
	}
	return millis, sales
}

func GetTotalUnSyncSales(ctx *fasthttp.RequestCtx) {
	//adminId := ctx.Request.Header.Peek("admin_id")
	millis := time.Now().Unix() * 1000
	response := models.ResponseAny{Status: true, Millis: millis, Message: "success"}
	salesIdsStr := strings.Replace(string(ctx.PostArgs().Peek("sales_ids")), " ", "", -1)
	openShiftId := string(ctx.PostArgs().Peek("open_shift_id"))

	salesIdsStr = strings.Replace(salesIdsStr, "\n", "", -1)

	log.Debug("check total unsync sales, openShiftId : %v | sales Ids: %s", openShiftId, utils.SimplyToJson(salesIdsStr))
	if openShiftId == "" {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	salesIds := strings.Split(salesIdsStr, ",")
	log.Info("total salesIds: %d", len(salesIds))

	//	sql := `select sales_id
	//from sales
	//where open_shift_fkid = ?
	//  and sales_id not in `
	//	sql += "(" + strings.Repeat("?,", len(salesIds)-1) + "?)"
	//
	//	params := make([]any, 0)
	//	params = append(params, openShiftId)
	//	for _, id := range salesIds {
	//		params = append(params, strings.TrimSpace(id))
	//	}
	//
	//	data, err := db.QueryArray(sql, params...)
	//	if log.IfErrorSetStatus(ctx, err) {
	//		return
	//	}
	//

	sql := "select sales_id from sales where open_shift_fkid = ? "
	data, err := db.QueryArray(sql, openShiftId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}
	log.Info("total sales id db: %d", len(data))
	log.Info("sales ids in db: %s", utils.SimplyToJson(data))

	idsToPull := make([]string, 0)
	idsToPush := make([]string, 0)

	salesIdMap := make(map[string]int)
	for i, id := range salesIds {
		salesIdMap[strings.TrimSpace(id)] = i
	}

	for _, row := range data {
		if _, found := salesIdMap[utils.ToString(row["sales_id"])]; !found {
			idsToPull = append(idsToPull, utils.ToString(row["sales_id"]))
		}
	}

	salesIdMap = make(map[string]int)
	for i, sales := range data {
		salesIdMap[utils.ToString(sales["sales_id"])] = i
	}

	log.Info("salesIdMap: %d --> %v", len(salesIdMap), salesIdMap)
	for _, row := range salesIds {
		if _, found := salesIdMap[row]; !found {
			idsToPush = append(idsToPush, row)
		}
	}

	log.Info("total in db: %d | total from client: %d | total id to pull: %d | total id to push: %d", len(data), len(salesIds), len(idsToPull), len(idsToPush))
	response.Data = map[string]any{
		"ids_to_pull": idsToPull,
		"ids_to_push": idsToPush,
	}

	//if total is the same, but still need to push or pull, give a warning
	if len(data) == len(salesIds) && len(idsToPull) != len(idsToPush) {
		log.IfError(fmt.Errorf("total from user & in db is same, but still neet to push/pull : total in db: %d | total from client: %d | total id to pull: %d | total id to push: %d", len(data), len(salesIds), len(idsToPull), len(idsToPush)))
	}

	_ = json.NewEncoder(ctx).Encode(response)
}

func SaveCashRecap(ctx *fasthttp.RequestCtx) {
	adminId := ctx.Request.Header.Peek("admin_id")
	millis := time.Now().Unix() * 1000
	response := models.ResponseString{Status: true, Millis: millis, Message: "Data successfully inserted"}
	cashRecap := models.CashRecap{}

	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&cashRecap)
	log.IfError(err)

	log.Debug("Saving cash recap.... outlet : %d (%s) by %d openShift %d", cashRecap.OutletFkid, adminId, cashRecap.EmployeeFkid, cashRecap.OpenShiftFkid)

	err = db.WithTransaction(func(trx db.Transaction) error {
		resp, _ := trx.Insert("cash_recap", map[string]any{
			"cash_recap_id":   cashRecap.CashRecapID,
			"cash":            cashRecap.Cash,
			"card":            cashRecap.Card,
			"sales_total":     cashRecap.SalesTotal,
			"outlet_fkid":     cashRecap.OutletFkid,
			"employee_fkid":   cashRecap.EmployeeFkid,
			"time_created":    cashRecap.TimeCreated,
			"time_modified":   millis,
			"previous_recap":  cashRecap.PreviousRecap,
			"open_shift_fkid": cashRecap.OpenShiftFkid,
		})

		lastId, _ := resp.LastInsertId()
		log.Info("cash recap Id: %v | lastId : %v", cashRecap.CashRecapID, lastId)

		//update time_close open_shift
		_, _ = trx.Update("open_shift", map[string]any{
			"time_close":  cashRecap.TimeCreated,
			"cash_drawer": cashRecap.Card + cashRecap.Cash,
		}, "open_shift_id = ? ", cashRecap.OpenShiftFkid)

		return nil
	})

	if err != nil {
		sqlErrCode := utils.ToInt(utils.GetErrCodeSQL(err.Error()))
		log.Info("error type : '%v' | code: %d", reflect.TypeOf(err), sqlErrCode)
		if driverErr, ok := err.(*mysql.MySQLError); ok {
			if driverErr.Number == 1062 {
				sqlErrCode = utils.ERR_SQL_DUPLICATE_PRIMARY
			}
		}

		if sqlErrCode == utils.ERR_SQL_DUPLICATE_PRIMARY {
			log.Warn("open shift id '%d' already in DB, skip....", cashRecap.OpenShiftFkid)
			_ = json.NewEncoder(ctx).Encode(models.ResponseString{Status: true, Millis: millis, Message: "your update wont be saved! the same key already inserted"})
			return
		}
	}

	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	go func(openShiftId int) {
		//wait for a couple seconds, gives android to finish printing
		//time.Sleep(5 * time.Second)

		//update device status to off
		_, err = db.Update("devices", map[string]any{
			"device_status": "off",
		}, "open_shift_fkid = ? ", openShiftId)
		log.IfError(err)
	}(cashRecap.OpenShiftFkid)

	//calculating pending bill
	go func(id int64) {
		data, err := db.Query("select sum(grand_total) as total from tmp_sales where outlet_fkid = ? and status = 'pending' ", cashRecap.OutletFkid)
		log.IfError(err)

		_, err = db.Update("cash_recap", map[string]any{
			"pending_bill_total": utils.ToInt(data["total"]),
			"time_modified":      time.Now().UnixNano() / 1e6,
		}, "cash_recap_id = ?", id)
		log.IfError(err)
	}(cashRecap.CashRecapID)

	//calculate sharing commission
	go func(cashRecapId int64, openShiftId, outletId int) {
		outlet, err := db.Query("SELECT commission_sharing from outlets where outlet_id=?", outletId)
		commission := cast.ToFloat(outlet["commission_sharing"])
		if log.IfError(err) || commission == 0 {
			log.Info("sharingCommission outlet %v: %v / %v", outletId, commission, outlet["commission_sharing"])
			return
		}

		sql := `SELECT sum(grand_total) as total from sales 
		where payment not in ('DUTY MEALS', 'COMPLIMENT') and open_shift_fkid=?`
		data, err := db.Query(sql, openShiftId)
		log.IfError(err)

		totalSharing := cast.ToFloat(data["total"]) * (commission / 100)
		log.Info("sharingCommission outlet %v: %v, grandTotal: %v -> %v", outletId, commission, data["total"], totalSharing)
		log.IfError(err)

		if totalSharing > 0 {
			_, err = db.Update("cash_recap", map[string]any{
				"outlet_commission": totalSharing,
				"time_modified":     time.Now().UnixNano() / 1e6,
			}, "cash_recap_id = ?", cashRecapId)
			log.IfError(err)
		}
	}((cashRecap.CashRecapID), (cashRecap.OpenShiftFkid), cashRecap.OutletFkid)

	//fmt.Println(">>> SaveCashRecap ID : ", cashRecap.CashRecapID, " | OutletID : ", cashRecap.OutletFkid)
	go SendReportCashRecap(cashRecap)
	go DecideToSendShiftRecap(cashRecap)

	log.Info("save cash recap with openShiftId '%d' finished", cashRecap.OpenShiftFkid)
	_ = json.NewEncoder(ctx).Encode(response)
}

func DecideToSendShiftRecap(recap models.CashRecap) {
	offset := int64(25200)

	//1. check if all shift closed
	//dateNow := time.Unix(time.Now().Unix()+offset, 0).Format("02-01-2006")
	dateNow := time.Unix(recap.TimeCreated/1000, 0).Format("02-01-2006")
	sql := `
select s.shift_id, s.name, os.time_open, os.time_close, o.name as outlet_name, s.admin_fkid
from shift s
         join shift_outlet so on s.shift_id = so.shift_fkid
         join outlets o on so.outlet_fkid = o.outlet_id
         left join (select shift_fkid, time_close, time_open
                    from open_shift
                    where from_unixtime(time_open / 1000 + 25200, '%d-%m-%Y') = ?
                      and outlet_fkid = ?) os
                   on os.shift_fkid = s.shift_id
where so.outlet_fkid = ? `
	data, err := db.QueryArray(sql, dateNow, recap.OutletFkid, recap.OutletFkid)
	log.IfError(err)

	if len(data) == 0 {
		log.Warn("something weird happen, user has no shift...")
		return
	}

	adminId := data[0]["admin_fkid"]
	isAllShiftClosed := true
	shiftCount := 0
	unClosedShift := make([]any, 0)
	for _, shift := range data {
		shiftCount++
		if shift["time_close"] == nil {
			log.Info("%s (%d) is not closed yet... | outlet : %s (%d) | openShiftId : %d | open at: %d", shift["name"], shift["shift_id"], shift["outlet_name"], recap.OutletFkid, recap.OpenShiftFkid, shift["time_open"])
			isAllShiftClosed = false
			unClosedShift = append(unClosedShift, shift["shift_id"])

			//should consider time open
			//ex: if shift usually open at 13:00, and now is 23:00, then disregard the shift
		}
	}

	//if not all closed, check if now is different day
	if !isAllShiftClosed {
		now := time.Now().Unix() + 25200
		data, err := db.Query("select from_unixtime(time_open/1000+25200, '%d-%m-%Y') as time_open, from_unixtime(?, '%d-%m-%Y') as time_now  from open_shift where open_shift_id = ? ", now, recap.OpenShiftFkid)
		log.IfError(err)
		fmt.Println("Data Open (Diff) ", data)
		//if day of time open shift is difference, consider as all shift is closed
		isAllShiftClosed = data["time_open"] != data["time_now"]
	}

	//check if unclosed shift is no longer use for more than 1 day
	if !isAllShiftClosed {
		sql = `
select max(time_close), from_unixtime(max(time_close) / 1000 + 25200, '%d-%m-%Y') as day,
       datediff(from_unixtime((select unix_timestamp(now())) + 25200, '%Y-%m-%d'),from_unixtime(max(time_close) / 1000 + 25200,'%Y-%m-%d')) as diff,
       shift_fkid
from open_shift
where shift_fkid in ([repeat]?) and outlet_fkid = ?
group by shift_fkid `
		sql = strings.Replace(sql, "[repeat]", strings.Repeat("?,", len(unClosedShift)-1), -1)
		param := make([]any, 0)
		param = append(param, unClosedShift...)
		param = append(param, recap.OutletFkid)
		dayDiffs, err := db.QueryArray(sql, param...)
		log.IfError(err)

		unClosedShiftCount := len(unClosedShift)
		log.Info("unclosed shift : %d --> %v", unClosedShiftCount, unClosedShift)
		for _, diff := range dayDiffs {
			fmt.Println(diff["diff"], diff["shift_fkid"])
			if utils.ToInt(diff["diff"]) > 1 {
				unClosedShiftCount--
			}
		}
		log.Info("unclosed shift [after checking] : %d", unClosedShiftCount)
		isAllShiftClosed = unClosedShiftCount == 0
	}

	//check if unclosed shift is usually closed in the next day

	if isAllShiftClosed {
		//fmt.Println("Sending....")
		if shiftCount > 1 {
			sendRecapAllShift(recap)
		}

		//try to wait, give time to the system to save close shift data
		time.Sleep(5 * time.Second)

		isAllow, err := DecideToSendShiftAllOutlet(utils.ToInt(adminId), time.Unix(time.Now().Unix()+offset, 0))
		if err == nil && isAllow {
			log.Info("Sending Shift Recap For All Outlets...")
			SendRecapAllOutlet(recap)
		} else {
			log.Info("not time to send all outlet report, err: %v", err)
		}
	}
}

func DecideToSendShiftAllOutlet(adminId int, dateToCheck time.Time) (bool, error) {
	dateNow := dateToCheck.Format("02-01-2006")

	//check if all outlet closed
	sql := `
select s.shift_id,
       s.name                                     as shift_name,
       so.admin_fkid,
       so.outlet_fkid,
       o.name                                     as outlet_name,
       os.open_shift_id,
       os.time_close,
       os.time_open,
       from_unixtime(os.time_open / 1000 + 25200) as time_open_format
from shift s
         join shift_outlet so on s.shift_id = so.shift_fkid
         join outlets o on so.outlet_fkid = o.outlet_id
         left join (select shift_fkid, time_close, open_shift_id, outlet_fkid, time_open
                    from open_shift
                    where from_unixtime(time_open / 1000 + 25200, '%d-%m-%Y') = ?) os
                   on os.outlet_fkid = so.outlet_fkid and os.shift_fkid = s.shift_id
where so.admin_fkid = ? `
	data, err := db.QueryArray(sql, dateNow, adminId)
	if utils.CheckErr(err) {
		return false, err
	}

	//if only have one outlet and one shift, return
	if len(data) == 1 {
		log.Info("only have one outlet and one shift, no need to send recap all outlet")
		return false, errors.New("user only have one outlet")
	}

	//check if have more than 1 outlet
	outlets := make(map[string]string)
	unClosedShift := make([]string, 0)
	unclosedShiftInfo := make(map[string][]string)
	idFormat := "%v#%v"
	for _, shift := range data {
		if shift["time_close"] == nil {
			unClosedShift = append(unClosedShift, fmt.Sprintf(idFormat, shift["outlet_fkid"], shift["shift_id"]))
			//log.Info("outlet %v (%d) belum menutup shift %v (%d)", shift["outlet_name"], shift["outlet_fkid"], shift["shift_name"], shift["shift_id"])
			key := fmt.Sprintf("shift %v (%d)", shift["shift_name"], shift["shift_id"])
			info := unclosedShiftInfo[key]
			info = append(info, fmt.Sprintf("outlet %v (%d)", shift["outlet_name"], shift["outlet_fkid"]))
			unclosedShiftInfo[key] = info
		} else {
			outlets[utils.ToString(shift["outlet_fkid"])] = utils.ToString("shift_id")
		}
	}
	log.Info("shift berikut belum ditutup: %s", utils.SimplyToJson(unclosedShiftInfo))

	decideToSend := len(unClosedShift) == 0 && len(outlets) > 1

	if !decideToSend && len(outlets) > 1 {
		//check if unclosed shift is not used since yesterday
		log.Info("check if unclosed shift is not used since yesterday. unclosed shift total : %d - closed outlet : %d", len(unClosedShift), len(outlets))
		unClosedCount := len(unClosedShift)
		dateYesterday := dateToCheck.AddDate(0, 0, -1).Format("02-01-2006")
		data, err := db.QueryArray(sql, dateYesterday, adminId)
		if log.IfError(err) {
			return false, err
		}

		for _, shift := range data {
			if shift["time_close"] == nil {
				for _, unclosed := range unClosedShift {
					if fmt.Sprintf(idFormat, shift["outlet_fkid"], shift["shift_id"]) == unclosed {
						unClosedCount--
					}
				}
			} else {
				for _, unclosed := range unClosedShift {
					if fmt.Sprintf(idFormat, shift["outlet_fkid"], shift["shift_id"]) == unclosed {
						log.Info("shift ini kemarin digunakan : %s (%d) - %s (%d) | dijam: %s", shift["shift_name"], shift["shift_id"], shift["outlet_name"], shift["outlet_fkid"], shift["time_open_format"])
						//if on yesterday the shift opened 7 hours before now, ignore it
						lastTimeOpen := time.Unix(utils.ToInt64(shift["time_open"])/1000, 0).Hour()
						hourDiff := time.Now().Hour() - lastTimeOpen
						log.Info("yesterday, open at: %d, now: %d | diff: %d", lastTimeOpen, time.Now().Hour(), hourDiff)
						if hourDiff > 7 {
							unClosedCount--
						}
					}
				}
			}
		}

		log.Info("FINAL unclosed shift total : %d", unClosedCount)
		decideToSend = unClosedCount <= 0
	}

	log.Info("final decision, should send all outlet report ? %v", decideToSend)
	return decideToSend, nil
}

func sendRecapAllShift(recap models.CashRecap) {
	fmt.Println("Sending recap all shift.... outlet: ", recap.OutletFkid)

	data, err := db.Query("select time_open from open_shift where open_shift_id = ?", recap.OpenShiftFkid)
	log.IfError(err)

	dateSecond, err := strconv.ParseInt(utils.ToString(data["time_open"]), 10, 64)
	log.IfError(err)
	dateFirst := time.Unix(dateSecond/1000, 0).AddDate(0, 0, -1).Unix() * 1000

	fmt.Println("Date 1 :  ", dateFirst, " | Date 2 : ", dateSecond)

	sql := `select cr.*, s.name from cash_recap cr
join open_shift os on cr.open_shift_fkid = os.open_shift_id
join shift s on os.shift_fkid = s.shift_id
where from_unixtime(os.time_open/1000+25200,'%d-%m-%Y') = from_unixtime( ? /1000+25200, '%d-%m-%Y')  and os.outlet_fkid = ? `
	cashRecapArr, err := db.QueryArray(sql, dateSecond, recap.OutletFkid)
	log.IfError(err)

	//if only 1 shift, dont send the report
	if len(cashRecapArr) == 1 {
		log.Info("Only 1 shift, no need to send recap")
		return
	}

	reportResult := ""
	outletName := ""
	phone := ""
	adminId := ""
	totalRecapToday := 0

	//wait for couple second, until 'SendReportCashRecap' finish doing calculation and saved the result to mongodb
	time.Sleep(3 * time.Second)

	//Get Recap for today
	for index, cashRecap := range cashRecapArr {
		cashRecapId := utils.ToInt64(cashRecap["cash_recap_id"])
		delete(cashRecap, "cash_recap_id")
		jsonByte, err := json.Marshal(cashRecap)
		log.IfError(err)

		recapCurrent := models.CashRecap{}
		err = json.Unmarshal(jsonByte, &recapCurrent)
		log.IfError(err)

		recapCurrent.CashRecapID = cashRecapId

		//report := CalculateShiftReport(recapCurrent)
		report := GetShiftReportFromCachedDb(recapCurrent.OpenShiftFkid)
		if report.DateTime == 0 { //just, in case failed to get from db, do the old fashion
			log.Error("failed to get shift from mongo-db (shift recap today) - openShiftId: %d", recapCurrent.OpenShiftFkid)
			report = CalculateShiftReport(recapCurrent)
		}
		if index == 0 {
			outletName = utils.ToString(report.Admin["outlet_name"])
			phone = utils.ToString(report.Admin["phone"])
			adminId = utils.ToString(report.Admin["admin_fkid"])
		}

		totalRecapToday += report.ItemSalesTotal
		reportResult += fmt.Sprintf("Shift %s : %s \n", cashRecap["name"], utils.CurrencyFormat(report.ItemSalesTotal))
	}

	//Get Recap for yesterday
	cashRecapArr, err = db.QueryArray(sql, dateFirst, recap.OutletFkid)
	log.IfError(err)

	totalRecapPreviousDay := 0
	//Get Recap for today
	for _, cashRecap := range cashRecapArr {
		cashRecapId := utils.ToInt64(cashRecap["cash_recap_id"])
		delete(cashRecap, "cash_recap_id")
		jsonByte, err := json.Marshal(cashRecap)
		log.IfError(err)

		recapCurrent := models.CashRecap{}
		err = json.Unmarshal(jsonByte, &recapCurrent)
		log.IfError(err)
		recapCurrent.CashRecapID = cashRecapId

		//report := CalculateShiftReport(recapCurrent)
		report := GetShiftReportFromCachedDb(recapCurrent.OpenShiftFkid)
		if report.DateTime == 0 { //just, in case failed to get from db, do the old fashion
			log.Error("failed to get shift from mongo-db (shift recap yesterday)")
			report = CalculateShiftReport(recapCurrent)
		}
		totalRecapPreviousDay += report.ItemSalesTotal
	}

	reportResult += fmt.Sprintf("Total : %s \n\n", utils.CurrencyFormat(totalRecapToday))

	log.Info("previous day : %d", totalRecapPreviousDay)
	growth := 100.0
	if totalRecapPreviousDay > 0 {
		growth = ((float64(totalRecapToday) - float64(totalRecapPreviousDay)) / float64(totalRecapPreviousDay)) * 100
	}

	if growth > 0 {
		//📈
		reportResult += fmt.Sprintf("Mengalami *Kenaikan* Sebesar %d%% dari hari sebelumnya", int(growth))
	} else if growth < 0 {
		//📉
		reportResult += fmt.Sprintf("Mengalami *Penurunan* Sebesar %d%% dari hari sebelumnya", int(growth*-1))
	}

	reportResult = "UNIQ POS - REKAP LAPORAN TUTUP KASIR " + time.Unix(dateSecond/1000, 0).Format("02-01-2006") + "\n" +
		"#Outlet " + outletName + " \n\n" + reportResult

	//_ = utils.SendWhatsAppMessage(reportResult, "secure", adminId, utils.Encrypt(phone))
	//_ = utils.SendMessageToGateWay(reportResult, phone, 0)
	_, err = db.Insert("scheduled_message", map[string]any{
		"title":        "CLOSE SHIFT RECAP REPORT",
		"message":      reportResult,
		"media":        "whatsapp",
		"receiver":     phone,
		"time_deliver": time.Now().Unix() * 1000,
		"data_created": time.Now().Unix() * 1000,
	})

	//send to others
	sql = `
select rra.media, rra.address
from report_recipient rr
         join report_recipient_type rrt on rr.report_recipient_id = rrt.report_recipient_fkid
         join report_recipient_outlet rro on rr.report_recipient_id = rro.report_recipient_fkid
         join report_recipient_address rra on rr.report_recipient_id = rra.report_recipient_fkid
where outlet_fkid = ? and admin_fkid = ? and report_type_fkid='recap_shift' and media='whatsapp' `
	receivers, err := db.QueryArray(sql, recap.OutletFkid, adminId)
	if log.IfError(err) {
		return
	}

	for _, receiver := range receivers {
		//_ = utils.SendWhatsAppMessage(reportResult, "secure", "", utils.Encrypt(utils.ToString(receiver["address"])))
		//_ = utils.SendMessageToGateWay(reportResult, utils.ToString(receiver["address"]), 0)
		_, err = db.Insert("scheduled_message", map[string]any{
			"title":        "CLOSE SHIFT RECAP REPORT",
			"message":      reportResult,
			"media":        "whatsapp",
			"receiver":     receiver["address"],
			"time_deliver": time.Now().Unix() * 1000,
			"data_created": time.Now().Unix() * 1000,
		})
		log.IfError(err)
	}

	//sql := ``
}

func CalculateShiftReport(recap models.CashRecap) models.CloseShiftReport {
	var wg sync.WaitGroup
	salesChan := make(chan []map[string]any, 1)
	salesDetailChan := make(chan []map[string]any, 1)
	paymentChan := make(chan []map[string]any, 1)
	voidChan := make(chan []map[string]any, 1)
	refundChan := make(chan []map[string]any, 1)
	taxChan := make(chan []map[string]any, 1)
	shiftChan := make(chan map[string]any)
	qtySalesChan := make(chan map[string]any)
	adminChan := make(chan map[string]any)
	employeeChan := make(chan map[string]any)
	salesPromotionChan := make(chan map[string]any)
	salesDetailPromotionChan := make(chan map[string]any)
	totalOpeationalCostChan := make(chan map[string]any)
	outletChan := make(chan map[string]any)

	log.Info("CalculateShiftReport: %s", utils.SimplyToJson(recap))

	cashRecap, err := db.Query("select * from cash_recap where cash_recap_id = ?", recap.CashRecapID)
	log.IfError(err)

	if len(cashRecap) == 0 {
		log.Error("no cash_recap with id: %v", recap.CashRecapID)
		return models.CloseShiftReport{}
	}

	wg.Add(1)
	sql := `SELECT s.*
FROM sales s
WHERE open_shift_fkid = ?
  and status = 'success'`
	go db.QueryArrayGo(&wg, salesChan, sql, cashRecap["open_shift_fkid"])

	wg.Add(1)
	sql = `SELECT sd.*
FROM sales_detail sd
         JOIN sales s ON s.sales_id = sd.sales_fkid
WHERE s.open_shift_fkid = ? `
	go db.QueryArrayGo(&wg, salesDetailChan, sql, cashRecap["open_shift_fkid"])

	wg.Add(1)
	go db.QueryArrayGo(&wg, paymentChan, "SELECT sp.* FROM sales_payment sp JOIN sales s ON s.sales_id=sp.sales_fkid  WHERE s.status='Success' AND s.open_shift_fkid = ? ", cashRecap["open_shift_fkid"])

	wg.Add(1)
	sql = `
SELECT sv.*, sd.child_type, s.status
FROM sales_void sv
         JOIN sales s ON sv.sales_fkid = s.sales_id
         join sales_detail sd on sv.sales_detail_fkid = sd.sales_detail_id
WHERE s.open_shift_fkid = ? `
	go db.QueryArrayGo(&wg, voidChan, sql, cashRecap["open_shift_fkid"])

	wg.Add(1)
	go db.QueryArrayGo(&wg, refundChan, "SELECT sr.* FROM sales_refund sr JOIN sales s ON sr.sales_fkid=s.sales_id WHERE s.open_shift_fkid = ? ", cashRecap["open_shift_fkid"])

	wg.Add(1)
	go db.QueryArrayGo(&wg, taxChan, "select st.* from sales_tax st join sales s on st.sales_fkid = s.sales_id where open_shift_fkid = ? ", cashRecap["open_shift_fkid"])

	go db.QueryChan(shiftChan, "select s.name, s.shift_id from open_shift os join shift s on s.shift_id=os.shift_fkid where os.open_shift_id = ? ", cashRecap["open_shift_fkid"])

	go db.QueryChan(qtySalesChan, "select sum(qty) as qty from sales_detail sd "+
		"join sales s on sd.sales_fkid = s.sales_id "+
		"where open_shift_fkid = ? and status='success' "+
		"and s.payment != 'compliment' and s.payment != 'duty meals'", cashRecap["open_shift_fkid"])

	go db.QueryChan(adminChan, "select a.phone, a.email, a.admin_id, o.outlet_id, o.admin_fkid, o.name AS outlet_name from outlets o join admin a on o.admin_fkid = a.admin_id where o.outlet_id = ? ", cashRecap["outlet_fkid"])

	go db.QueryChan(employeeChan, "SELECT employee_id,name,email,phone FROM employee WHERE employee_id=?", cashRecap["employee_fkid"])

	sql = `select sum(sp.promotion_value) as total_promotion
from sales_promotion sp
         join sales s on s.sales_id = sp.sales_fkid
where s.open_shift_fkid = ?
  and s.status = 'success' `
	go db.QueryChan(salesPromotionChan, sql, cashRecap["open_shift_fkid"])

	sql = `select sum(sdp.promotion_value) - coalesce(sum(abs(sv.promotion_value)), 0) as total_promotion
from sales_detail sd
         join sales_detail_promotion sdp on sd.sales_detail_id = sdp.sales_detail_fkid
         left join (
    select min(sv.sales_detail_fkid) as sales_detail_fkid, sum(p.promotion_value) as promotion_value
    from sales_void sv
             join sales_detail_promotion p on sv.sales_void_id = p.sales_void_fkid
    group by sv.sales_detail_fkid
) sv on sv.sales_detail_fkid = sd.sales_detail_id
         join sales s on sd.sales_fkid = s.sales_id
where s.open_shift_fkid = ?
  and s.status = 'success' `
	go db.QueryChan(salesDetailPromotionChan, sql, cashRecap["open_shift_fkid"])

	sql = `
select sum(total) as total
from operationalcost
where outlet_fkid = ?
  and (unix_timestamp(data_created) * 1000) between (select time_open from open_shift where open_shift_id = ?) and
        (select time_close from open_shift where open_shift_id = ?)`
	go db.QueryChan(totalOpeationalCostChan, sql, cashRecap["outlet_fkid"], cashRecap["open_shift_fkid"], cashRecap["open_shift_fkid"])

	go db.QueryChan(outletChan, "SELECT commission_sharing from outlets where outlet_id = ? ", recap.OutletFkid)

	salesPaymentChan := make(chan []map[string]any, 1)
	sql = `SELECT sp.* from sales_payment sp 
JOIN sales ON sp.sales_fkid = sales.sales_id
where sales.open_shift_fkid = ? `
	wg.Add(1)
	go db.QueryArrayGo(&wg, salesPaymentChan, sql, cashRecap["open_shift_fkid"])

	wg.Wait()
	sales := <-salesChan
	salesDetail := <-salesDetailChan
	payments := <-paymentChan
	voids := <-voidChan
	refunds := <-refundChan
	taxes := <-taxChan
	shift := <-shiftChan
	qtySales := <-qtySalesChan
	employee := <-employeeChan
	data := <-adminChan
	salesPromotion := <-salesPromotionChan
	salesDetailPromotion := <-salesDetailPromotionChan
	totalOperationalCost := <-totalOpeationalCostChan
	outlet := <-outletChan
	salesPayment := <-salesPaymentChan

	qtySalesInt := utils.ToInt(qtySales["qty"])
	salesJson, err := json.Marshal(sales)
	log.IfError(err)

	salesDetailJson, err := json.Marshal(salesDetail)
	log.IfError(err)

	var salesArr []models.SalesDb
	err = json.Unmarshal(salesJson, &salesArr)
	log.IfError(err)

	var salesDetailArr []models.SalesDetail
	err = json.Unmarshal(salesDetailJson, &salesDetailArr)
	log.IfError(err)

	salesPaymentMap := array.GroupBy(salesPayment, "sales_fkid")
	log.Info("size SalesPayment %v, grouped: %v", len(salesPayment), len(salesPaymentMap))

	//voids, err := database.QueryArray("SELECT * FROM sales WHERE open_shift_fkid=?", recap.OpenShiftFkid)

	itemSalesQty, itemSalesTotal, discTotal := 0, 0, 0
	totalCash, totalCard, totalPiutang := int64(0), int64(0), int64(0)
	totalPax, billCount := 0, 0
	totalVoid, totalRefund := int64(0), int64(0)
	qtyVoid := 0

	for _, sale := range salesArr {
		qty := 0
		disc := 0
		for _, detail := range salesDetailArr {
			if sale.SalesID == detail.SalesFkid {
				//sale.SalesDetail = append(sale.SalesDetail, detail)
				qty += detail.Qty
				disc += detail.Discount
				for _, void := range voids {
					if utils.ToInt(void["sales_detail_fkid"]) == detail.SalesDetailID {
						qty -= utils.ToInt(void["qty"])
					}
				}
			}
		}

		if strings.Contains(sale.Payment, "CASH") || strings.Contains(sale.Payment, "CARD") { //|| strings.Contains(sale.Payment, "PIUTANG")
			itemSalesQty += qty
			// itemSalesTotal += sale.GrandTotal
			discTotal += disc + sale.Discount
		}

		salesPayments := salesPaymentMap[sale.SalesID]
		log.Info("sales %v as %v payments", sale.SalesID, len(salesPayments))
		for _, payment := range salesPayments {
			if method := cast.ToString(payment["method"]); method == "CASH" || method == "CARD" {
				itemSalesTotal += cast.ToInt(payment["total"])
			}
		}

		//if qty is 0, it's might be all item is void, and not consider in pax calculation
		if qty > 0 {
			totalPax += sale.QtyCustomers
			billCount++
		}
	}

	for _, void := range voids {
		if strings.ToLower(utils.ToString(void["status"])) == "success" {
			qtyVoid += utils.ToInt(void["qty"])
		}
		totalVoid += void["qty"].(int64) * void["price"].(int64)
	}

	banks := make(map[string]int64)
	for _, payment := range payments {
		if payment["method"] == "CASH" {
			totalCash += payment["total"].(int64)
		} else if payment["method"] == "CARD" {
			totalCard += payment["total"].(int64)

			bankDetail, err := db.Query("select * from sales_payment_bank spb join payment_media_bank pmb "+
				"on spb.bank_fkid = pmb.bank_id WHERE sales_payment_fkid=?", payment["payment_id"])
			log.IfError(err)

			if len(bankDetail) > 0 {
				banks[bankDetail["name"].(string)] += payment["total"].(int64)
			}
		} else if payment["method"] == "PIUTANG" {
			totalPiutang += payment["total"].(int64)
		}
	}

	for _, refund := range refunds {
		totalRefund += refund["grand_total"].(int64)
	}

	for _, tax := range taxes {
		if tax["category"] == "discount" {
			discTotal += utils.ToInt(tax["total"])
		}
	}

	log.Debug("sales promotion : %v | sales detail promotion : %v", salesPromotion["total_promotion"], salesDetailPromotion["total_promotion"])
	log.Debug("qty sales : %v | qty void : %v", qtySalesInt, qtyVoid)
	discTotal += utils.ToInt(salesPromotion["total_promotion"]) + utils.ToInt(salesDetailPromotion["total_promotion"])

	cardDetail := ""
	bankDetail := make([]models.BankDetail, 0)
	for bank, value := range banks {
		cardDetail += " -" + bank + "   " + utils.CurrencyFormat(int(value)) + "\n"
		bankDetail = append(bankDetail, models.BankDetail{BankName: bank, Total: int(value)})
	}

	avgPax := 0
	if totalPax > 0 {
		avgPax = itemSalesTotal / totalPax
	}

	//date, err := utils.MillisToDateTime(recap.TimeCreated/1000 + 25200)

	avgBill := itemSalesTotal
	if billCount > 0 {
		avgBill = itemSalesTotal / billCount
	}

	fmt.Println("itemSalesQty : ", itemSalesQty, "New Calculation : ", qtySalesInt-qtyVoid, " - couse : ", qtySalesInt, "-", qtyVoid)
	shiftReport := models.CloseShiftReport{}
	shiftReport.OpenShiftId = utils.ToInt64(cashRecap["open_shift_fkid"])
	shiftReport.Employee = employee
	shiftReport.Admin = data
	shiftReport.ItemSalesQty = qtySalesInt - qtyVoid //itemSalesQty
	shiftReport.ItemSalesTotal = itemSalesTotal
	shiftReport.DiscTotal = discTotal
	shiftReport.TotalCash = int(totalCash)
	shiftReport.TotalCard = int(totalCard)
	shiftReport.CardDetail = cardDetail
	shiftReport.BankDetail = bankDetail
	shiftReport.TotalPiutang = int(totalPiutang)
	shiftReport.TotalRefund = int(totalRefund)
	shiftReport.TotalVoid = cast.AbsInt(int(totalVoid))
	shiftReport.TotalPax = totalPax
	shiftReport.AvgPax = avgPax
	shiftReport.AvgBill = avgBill
	shiftReport.BillCount = billCount
	shiftReport.ShiftName = utils.ToString(shift["name"])
	shiftReport.Shift = models.Shift{ShiftId: utils.ToInt(shift["shift_id"]), ShiftName: utils.ToString(shift["name"])}
	shiftReport.OutletName = utils.ToString(data["outlet_name"])
	shiftReport.EmployeeName = utils.ToString(employee["name"])
	shiftReport.ActualCash = utils.ToInt(cashRecap["cash"])
	shiftReport.ActualCard = utils.ToInt(cashRecap["card"])
	shiftReport.TotalOperationalCost = utils.ToInt(totalOperationalCost["total"])
	shiftReport.Outlet = models.Outlet{OutletName: utils.ToString(data["outlet_name"]), OutletId: utils.ToInt(data["outlet_id"])}
	shiftReport.DateTime = time.Now().Unix() * 1000

	//sharing commission
	if commission := cast.ToFloat(outlet["commission_sharing"]); commission > 0 {
		shiftReport.CommissionPercentage = fmt.Sprintf("%v%%", commission)
		shiftReport.CommissionTotal = int(cast.ToFloat(itemSalesTotal) * (commission / 100))
	}

	fmt.Println("admin: ", data)

	return shiftReport
}

func SendReportCashRecap(recap models.CashRecap) {
	t := time.Unix(recap.TimeCreated/1000+25200, 0)
	date := t.Format("15:04 02-01-2006")

	shiftReport := CalculateShiftReport(recap)
	shiftReport.Date = date

	//save recap to db
	_, err := db.Mongo().Collection("close_shift").InsertOne(context.Background(), shiftReport)
	log.IfError(err)
	fmt.Printf("close shift %d saved to mongodb\n", shiftReport.OpenShiftId)

	templateWaString, err := utils.ReadFile("config/template/close_shift_template_wa.tmpl")
	if log.IfError(err) {
		return
	}

	templateEmailString, err := utils.ReadFile("config/template/close_shift_template.html")
	if log.IfError(err) {
		return
	}

	currencyFunc := template.FuncMap{
		"Currency": func(num int) string {
			return format.Currency(num)
		},
		"Add": func(nums ...int) int {
			result := 0
			for _, n := range nums {
				result += n
			}
			return result
		},
		"Minus": func(nums ...int) int {
			result := 0
			for i, n := range nums {
				if i == 0 {
					result = n
				} else {
					result -= n
				}
			}
			return result
		},
	}

	currencyFuncText := templateText.FuncMap{}
	for k, v := range currencyFunc {
		currencyFuncText[k] = v
	}

	tmplateWa, err := templateText.New("close_shift_report_wa").Funcs(currencyFuncText).Parse(templateWaString)
	if log.IfError(err) {
		fmt.Println("error - ", err)
	}

	tmplateEmail, err := template.New("close_shift_report_email").Funcs(currencyFunc).Parse(templateEmailString)
	if log.IfError(err) {
		fmt.Println("error - ", err)
	}

	//save total sales calculation to database
	if recap.CashRecapID > 0 {
		_, err = db.Update("cash_recap", map[string]any{
			"sales_total":   shiftReport.ItemSalesTotal,
			"time_modified": time.Now().UnixNano() / 1e6,
		}, "cash_recap_id = ?", recap.CashRecapID)
		log.IfError(err)
	} else {
		log.Warn("no cash recap id, sales total wont be updated!")
	}

	//check user subscription, warn user if subscription or trial is about to end
	subscriptionWarning := ""
	phone := shiftReport.Admin["phone"].(string)

	sub, err := GetSubscriptionDetail(shiftReport.Admin["admin_fkid"])
	log.IfError(err)

	warn := sub.GenerateMessage()

	if warn != "" {
		subscriptionWarning = warn
		log.Error("%s - %v \n%s", shiftReport.Admin["outlet_name"], shiftReport.Admin["admin_fkid"], subscriptionWarning)
		go autoGenerateInvoice(utils.ToInt(shiftReport.Admin["admin_fkid"]))
	}

	//if user has only one outlet and one shift, report the increase/decrease percentage
	////

	var reportWa bytes.Buffer
	err = tmplateWa.Execute(&reportWa, shiftReport)
	log.IfError(err)
	log.Info(reportWa.String())

	scheduleMsg := map[string]any{
		"title":        "CLOSE SHIFT REPORT",
		"message":      reportWa.String() + subscriptionWarning,
		"media":        "whatsapp",
		"receiver":     phone,
		"time_deliver": time.Now().Unix() * 1000,
		"data_created": time.Now().Unix() * 1000,
	}

	// err = google.PublishMessage(scheduleMsg, "messaging-gateway-production")
	// if err != nil {
	_, err = db.Insert("scheduled_message", scheduleMsg)
	log.IfError(err)
	// }

	var reportEmail bytes.Buffer
	err = tmplateEmail.Execute(&reportEmail, shiftReport)
	log.IfError(err)

	//_, err = db.Insert("scheduled_message", map[string]any{
	//	"title":        "CLOSE SHIFT REPORT",
	//	"message":      reportEmail.String(),
	//	"media":        "email",
	//	"receiver":     shiftReport.Admin["email"],
	//	"time_deliver": time.Now().Unix() * 1000,
	//	"data_created": time.Now().Unix() * 1000,
	//})

	sql := `
select coalesce(ej.phone, rraj.address, ej.email) as address,
       (case
            when ej.phone is not null then 'whatsapp'
            when ej.email is not null then 'email'
            else rraj.media
           end) as media
from report_recipient rr
         join report_recipient_type rrt on rr.report_recipient_id = rrt.report_recipient_fkid
         left join (
    select e.employee_id, e.phone, e.email
    from employee e
             join employee_outlet eo on e.employee_id = eo.employee_fkid
    where eo.outlet_fkid = ?
) ej on ej.employee_id = rr.employee_fkid
         left join (
    select rra.report_recipient_fkid, rra.media, rra.address
    from report_recipient_address rra
             join report_recipient_outlet rro on rra.report_recipient_fkid = rro.report_recipient_fkid
    where outlet_fkid = ?
) rraj on rraj.report_recipient_fkid = rr.report_recipient_id
where rr.admin_fkid = ?
  and rrt.report_type_fkid = 'close_shift'
having address is not null `

	receivers, err := db.QueryArray(sql, recap.OutletFkid, recap.OutletFkid, shiftReport.Admin["admin_fkid"])
	if log.IfError(err) {
		return
	}

	for _, receiver := range receivers {
		if utils.ToString(receiver["media"]) == "whatsapp" {
			msgContent := reportWa.String()
			if utils.ToInt(shiftReport.Admin["admin_fkid"]) == 297 {
				msgContent += subscriptionWarning
			}

			scheduleMsg = map[string]any{
				"title":        "CLOSE SHIFT REPORT",
				"message":      msgContent,
				"media":        "whatsapp",
				"receiver":     utils.ToString(receiver["address"]),
				"time_deliver": time.Now().Unix() * 1000,
				"data_created": time.Now().Unix() * 1000,
			}
			// err = google.PublishMessage(scheduleMsg, "messaging-gateway-production")
			// if err != nil {
			_, err = db.Insert("scheduled_message", scheduleMsg)
			log.IfError(err)
			// }
		} else if utils.ToString(receiver["media"]) == "email" {
			_, err = db.Insert("scheduled_message", map[string]any{
				"title":        "CLOSE SHIFT " + strings.ToUpper(shiftReport.OutletName),
				"message":      utils.MinifyHtml(reportEmail.String()),
				"media":        "email",
				"receiver":     utils.ToString(receiver["address"]),
				"time_deliver": time.Now().Unix() * 1000,
				"data_created": time.Now().Unix() * 1000,
			})
			log.IfError(err)
		} else {
			log.Info("we will not send to %s", receiver["address"])
		}
	}
}

var muInvoice sync.Mutex

func autoGenerateInvoice(adminId int) {
	muInvoice.Lock()
	defer muInvoice.Unlock()

	//give some delays, so the closing report sent first
	time.Sleep(5 * time.Second)

	log.Info("request to generate invoice for adminId: %v", adminId)
	req := utils.HttpRequest{
		Method: "POST",
		Url:    fmt.Sprintf("%s/public/v1/generate-invoice", os.Getenv("API_BILLING")),
		PostRequest: utils.PostRequest{
			Form: map[string]string{
				"admin_id": utils.ToString(adminId),
			},
		},
	}
	_, err := req.Execute()
	log.IfError(err)
}

func GetOpenShift(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")

	if valid := auth.ValidateOutlet(ctx); !valid {
		return
	}

	//this will not necessary anymore
	if lastSync == nil {
		lastSync = 0
	}

	//This just temporary
	//lastSync = 1542214800000

	data, err := db.QueryArray("SELECT * FROM open_shift WHERE outlet_fkid=? AND data_modified >= ?", outletId, lastSync)
	utils.CheckErr(err)

	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: millis, Data: data})
}

var openingShift = make(map[int]bool)

func SaveOpenShift(ctx *fasthttp.RequestCtx) {
	adminId := ctx.Request.Header.Peek("admin_id")
	millis := time.Now().Unix() * 1000
	openShift := models.OpenShift{}
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&openShift)
	utils.CheckErr(err)

	if !auth.ValidateOutletId(ctx, openShift.OutletFkid) {
		return
	}

	if openingShift[openShift.OutletFkid] {
		log.Error("duplicate open shift by outlet : %d", openShift.OutletFkid)
		return
	}
	openingShift[openShift.OutletFkid] = true

	data := make(map[string]any, 0)
	response := models.Response{Status: true, Millis: millis, Data: data}
	count, err := db.Query("SELECT open_shift_id FROM open_shift WHERE time_close IS NULL AND outlet_fkid=? LIMIT 1", openShift.OutletFkid)
	utils.CheckErr(err)

	if len(count) > 0 {
		response.Status = false
		response.Code = 1
		response.Message = "Another shift is still active"
		data["open_shift_id"] = count["open_shift_id"]
	} else {
		date := time.Unix(time.Now().Unix()+25200, 0).Format("2006-01-02 15:04:05")
		fmt.Println(date, " OpenShiftId from Device : ", openShift.OpenShiftId, " | OutletId : ", openShift.OutletFkid, " | TimeOpen : ", openShift.TimeOpen)

		data := map[string]any{
			//"open_shift_id" : openShift.OpenShiftId,  --> if using offline mode
			"outlet_fkid":   openShift.OutletFkid,
			"employee_fkid": openShift.EmployeeFkid,
			"early_cash":    openShift.EarlyCash,
			"shift_fkid":    openShift.ShiftFkid,
			"time_open":     millis, //openShift.TimeOpen, --> if using offline mode
			"data_created":  time.Now().Unix() * 1000,
			"data_modified": time.Now().Unix() * 1000,
		}
		lastId, _ := db.Query("SELECT MAX(open_shift_id) as id from open_shift where open_shift_id < 10000")
		if utils.ToString(lastId["id"]) != "9999" {
			lastIdInt, _ := strconv.Atoi(utils.ToString(lastId["id"]))
			data["open_shift_id"] = lastIdInt + 1
		}

		//if openShift.OpenShiftId <= 0 {
		//	delete(data, "open_shift_id")
		//}

		res, err := db.Insert("open_shift", data)
		if log.IfError(err) {
			ctx.SetStatusCode(fasthttp.StatusInternalServerError)
			openingShift[openShift.OutletFkid] = false
			return
		}

		id, err := res.LastInsertId()
		data["open_shift_id"] = id
		data["early_cash"] = openShift.EarlyCash
		response.Data = data

		//update device info
		go func(deviceId, deviceName, adminId string, outletId int) {
			dataOpenShift, err := db.QueryArray("SELECT imei FROM devices WHERE imei=?", deviceId)
			log.IfError(err)
			if len(dataOpenShift) > 0 {
				_, err = db.Update("devices", map[string]any{
					"device_status":   "on",
					"open_shift_fkid": id,
				}, "imei = ?", deviceId)
				log.IfError(err)
			} else {
				_, err := db.Insert("devices", map[string]any{
					"imei":            deviceId,
					"outlet_fkid":     outletId,
					"admin_fkid":      adminId,
					"name":            deviceName,
					"device_status":   "on",
					"open_shift_fkid": id,
					"data_created":    time.Now().Unix() * 1000,
					"data_modified":   time.Now().Unix() * 1000,
				})
				log.IfError(err)
			}
		}(openShift.DeviceID, openShift.DeviceName, string(adminId), openShift.OutletFkid)
	}

	openingShift[openShift.OutletFkid] = false
	_ = json.NewEncoder(ctx).Encode(response)
}

func SaveSalesCart(ctx *fasthttp.RequestCtx) {
	response := models.ResponseString{Status: true, Millis: time.Now().Unix() * 1000}
	salesCart := models.SalesCart{}
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&salesCart)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	if !auth.ValidateOutletId(ctx, salesCart.OutletFkid) {
		return
	}

	// salesCart.Sales = utils.RemoveAllEmoji(salesCart.Sales)

	count, err := db.Query("SELECT no_nota, device_edit_mode, status FROM tmp_sales WHERE no_nota=?", salesCart.NoNota)
	log.IfError(err)

	log.Info("saving sales cart, id '%s' to --> %s | status in db: %v", salesCart.NoNota, salesCart.Status, count["status"])

	grandTotal := 0
	if salesCart.Sales != "" {
		sales := models.Sale{}
		err = json.Unmarshal([]byte(salesCart.Sales), &sales)
		log.IfError(err)
		if err != nil {
			fmt.Println("JSON : ", salesCart.Sales)
		}
		grandTotal = sales.GrandTotal
	}

	data := map[string]any{
		"status":        salesCart.Status,
		"sales":         utils.RemoveAllEmoji(salesCart.Sales),
		"grand_total":   grandTotal,
		"time_modified": time.Now().Unix() * 1000,
	}

	if len(count) > 0 {
		if count["status"] != "paid" {
			if count["device_edit_mode"] == string(ctx.Request.Header.Peek("Device")) {
				data["start_edit_mode"] = 0
			}
			_, err := db.UpdateDb("tmp_sales", data, map[string]any{"no_nota": salesCart.NoNota})
			if log.IfErrorSetStatus(ctx, err) {
				return
			}
			response.Message = "Data (sales cart) successfully updated!"
		} else {
			response.Message = "You can not update paid sales cart!"
		}
	} else {
		data["no_nota"] = salesCart.NoNota
		data["time_created"] = salesCart.TimeCreated
		data["outlet_fkid"] = salesCart.OutletFkid
		_, err := db.Insert("tmp_sales", data)
		if log.IfErrorSetStatus(ctx, err) {
			fmt.Println("Error insert this : ", utils.SimplyToJson(data))
			return
		}
		response.Message = "Data successfully inserted!"
	}

	log.Info("result saving sales cart '%s' -> %s", salesCart.NoNota, response.Message)
	_ = json.NewEncoder(ctx).Encode(response)
}

func GetSalesCart(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")

	// log.Info("fetch cart: %v | %v", outletId, lastSync)
	if valid := auth.ValidateOutlet(ctx); !valid {
		log.Info("can not fetch cart, unauth outletId=%s", outletId)
		return
	}

	data, err := db.QueryArray("SELECT * FROM tmp_sales WHERE outlet_fkid=? AND time_modified>=?", outletId, lastSync)
	log.IfError(err)

	if outletId == 263 {
		log.Info("fetch cart, size: %v | %v", len(data), lastSync)
	}

	// log.Info("fetch cart: %v, result %v", outletId, len(data))
	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: millis, Data: data})
}

func UpdateSalesCart(ctx *fasthttp.RequestCtx) {
	response := models.ResponseString{Status: true, Millis: time.Now().Unix() * 1000}
	salesCart := models.SalesCart{}
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&salesCart)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	if !auth.ValidateOutletId(ctx, salesCart.OutletFkid) {
		return
	}

	count, err := db.Query("SELECT sales_id FROM sales WHERE sales_id = ? ", salesCart.NoNota)
	fmt.Println("UpdateSalesCart. Len : ", len(count))
	if len(count) == 0 {
		dataDb, err := db.Query("SELECT device_edit_mode FROM tmp_sales WHERE no_nota = ? LIMIT 1", salesCart.NoNota)
		log.IfError(err)

		data := map[string]any{
			"status":        salesCart.Status,
			"sales":         salesCart.Sales,
			"time_modified": time.Now().Unix() * 1000,
		}

		if dataDb["device_edit_mode"] == string(ctx.Request.Header.Peek("Device")) {
			data["start_edit_mode"] = 0
		}

		res, err := db.UpdateDb("tmp_sales", data, map[string]any{"no_nota": salesCart.NoNota})

		/*res, err := database.GetDb().Exec("INSERT INTO tmp_sales (no_nota, status, time_created, time_modified, outlet_fkid, sales) "+
		"VALUES (?,?,?,?,?,?)", salesCart.NoNota, salesCart.Status, salesCart.TimeCreated, time.Now().Unix()*1000, salesCart.OutletFkid, salesCart.Sales)*/
		if log.IfErrorSetStatus(ctx, err) {
			return
		}

		row, err := res.RowsAffected()
		if row > 0 {
			response.Message = "Data successfully updated!"
		} else {
			response.Message = "No data were updated!"
		}
	} else {
		response.Message = "this transaction has been paid before!"
	}

	_ = json.NewEncoder(ctx).Encode(response)
}

func SaveRefund(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Status: true, Millis: time.Now().Unix() * 1000, Message: "Success"}
	//adminId := ctx.Request.Header.Peek("admin_id")
	refund := models.Refund{}
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&refund)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	log.Info("saving refund of '%s' -> %v", refund.SalesID, cast.ToJson(ctx.PostBody()))

	if refund.EmployeeID == 0 && refund.EmployeeFkId != 0 {
		refund.EmployeeID = refund.EmployeeFkId
	}

	//if refund.SalesID == "15I494MN0A8KI" {
	//	log.Info("REFUND not inserted because id is %s", refund.SalesID)
	//	return
	//}

	count, err := db.Query("select sr.sales_refund_id from sales s left join sales_refund sr on s.sales_id = sr.sales_fkid where sales_id=?", refund.SalesID)
	log.IfError(err)

	//if data not yet inserted to table sales, skip...
	if len(count) == 0 {
		log.Info("refund of '%s' skip sales is not yet inserted...", refund.SalesID)
		response.Status = false
		response.Message = "sales is not yet inserted"
	}

	if len(count) > 0 && count["sales_refund_id"] == nil {
		_, err = db.Insert("sales_refund", array.RemoveEmpty(map[string]any{
			"sales_fkid":    refund.SalesID,
			"time_created":  refund.TimeMillis,
			"time_modified": time.Now().Unix() * 1000,
			"grand_total":   refund.GrandTotal,
			"employee_fkid": refund.EmployeeID,
			"reason":        refund.Reason,
		}))

		if err != nil {
			errCode := utils.ToInt(utils.GetErrCodeSQL(err.Error()))
			if errCode == utils.ERR_SQL_DUPLICATE_PRIMARY {
				log.Info("it seems that sales '%s' already been refunded", refund.SalesID)
				_, err = db.Update("sales", map[string]any{
					"status":        "Refund",
					"time_modified": utils.CurrentMillis(),
				}, "sales_id = ?", refund.SalesID)
				log.IfError(err)
				_ = json.NewEncoder(ctx).Encode(response)
				return
			} else if errCode == utils.ERR_SQL_LOCK_TIMEOUT || errCode == utils.ERR_SQL_DEADLOCK {
				ctx.SetStatusCode(fasthttp.StatusInternalServerError)
				return
			}
		}

		if log.IfErrorSetStatus(ctx, err) {
			return
		}

		_, err = db.Update("sales", map[string]any{
			"status":        "Refund",
			"time_modified": utils.CurrentMillis(),
		}, "sales_id = ?", refund.SalesID)
		log.IfError(err)

		if utils.IsAllowedToIntegrateWithBehave() {
			go func() {
				count, err = db.Query("SELECT sales_promo_id, info FROM sales_promo WHERE sales_fkid = ? and source = 'behave' ", refund.SalesID)
				utils.CheckErr(err)

				sale, err := db.Query("select display_nota, e.name from sales s join employee e on s.employee_fkid = e.employee_id WHERE sales_id = ?", refund.SalesID)
				utils.CheckErr(err)

				if len(count) > 0 {
					behave.PushRefundTransaction(models.Sale{
						DisplayNota:  utils.ToString(sale["display_nota"]),
						EmployeeName: utils.ToString(sale["name"]),
					})
				}
			}()
		}
	}

	_ = json.NewEncoder(ctx).Encode(response)
}

func GetReservationByOutlet(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Status: true, Millis: time.Now().Unix() * 1000, Message: "Success"}
	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")

	if valid := auth.ValidateOutlet(ctx); !valid {
		return
	}

	data, err := db.QueryArray("SELECT * FROM reservation WHERE outlet_fkid=? AND time_modified>=?", outletId, lastSync)
	utils.CheckErr(err)

	response.Data = data
	_ = json.NewEncoder(ctx).Encode(response)
}

func SaveReservation(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Status: true, Millis: time.Now().Unix() * 1000, Message: "Success"}
	reservation := models.Reservation{}

	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&reservation)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	res, err := db.GetDb().Exec("INSERT INTO reservation (outlet_fkid, name, phone, time_start, "+
		"time_end, time_created, time_modified) VALUES (?,?,?,?,?,?,?)", reservation.OutletFkid, reservation.Name, reservation.Phone, reservation.TimeStart,
		reservation.TimeEnd, reservation.TimeCreated, time.Now().Unix()*1000)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	id, _ := res.LastInsertId()

	response.Data = map[string]any{
		"id": id,
	}

	_ = json.NewEncoder(ctx).Encode(response)
}

func GetPendingBillStatus(ctx *fasthttp.RequestCtx) {
	salesId := ctx.UserValue("salesId")
	deviceId := string(ctx.Request.Header.Peek("Device"))
	data, err := db.Query("SELECT * from tmp_sales WHERE no_nota = ? LIMIT 1", salesId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	maxEditSession := 10 //in minute
	isEnableEdit := utils.ToString(data["device_edit_mode"]) == deviceId
	message := "success"

	if !isEnableEdit {
		startEditInt, err := strconv.ParseInt(utils.ToString(data["start_edit_mode"]), 10, 64)
		if err != nil {
			startEditInt = 0
		}

		startEdit := time.Unix(startEditInt/1000, 0)
		duration := time.Since(startEdit)
		isEnableEdit = duration.Minutes() > float64(maxEditSession)
		if !isEnableEdit {
			device, err := db.Query("SELECT name FROM devices WHERE imei = ? LIMIT 1", utils.ToString(data["device_edit_mode"]))
			utils.CheckErr(err)
			deviceName := utils.ToString(device["name"])
			if device["name"] == nil {
				deviceName = "other device!"
			}
			message = fmt.Sprintf("Transaction is being edited by '%s' \nPlease wait until it's done! \n\nor wait for %d minutes", deviceName, maxEditSession-int(duration.Minutes()))
		}
	}

	if isEnableEdit {
		_, err = db.UpdateDb("tmp_sales", map[string]any{
			"start_edit_mode":  time.Now().Unix() * 1000,
			"device_edit_mode": deviceId,
		}, map[string]any{"no_nota": salesId})
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: isEnableEdit, Data: data, Message: message})
}

func SendCashRecapReport(ctx *fasthttp.RequestCtx) {
	cashRecap := models.CashRecap{}
	cashRecap.CashRecapID, _ = strconv.ParseInt(string(ctx.Request.PostArgs().Peek("cash_recap_id")), 10, 64)
	data, err := db.Query("SELECT * FROM cash_recap WHERE cash_recap_id = ?", cashRecap.CashRecapID)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	if len(data) > 0 {
		cashRecap.OpenShiftFkid, _ = strconv.Atoi(utils.ToString(data["open_shift_fkid"]))
		cashRecap.OutletFkid, _ = strconv.Atoi(utils.ToString(data["outlet_fkid"]))
		cashRecap.EmployeeFkid, _ = strconv.Atoi(utils.ToString(data["employee_fkid"]))
		cashRecap.Cash, _ = strconv.Atoi(utils.ToString(data["cash"]))
		cashRecap.Card, _ = strconv.Atoi(utils.ToString(data["card"]))
		cashRecap.TimeCreated, _ = strconv.ParseInt(utils.ToString(data["time_created"]), 10, 64)
		SendReportCashRecap(cashRecap)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Data: data, Message: "report send successfully!"})
	} else {
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "data not found!"})
	}
}

func addMemberFromBehave(member models.MemberBehave, adminId string) {
	if strings.HasPrefix(member.Phone, "08") {
		member.Phone = "62" + member.Phone[1:]
	}

	if member.Phone == "" {
		fmt.Println("user has no phone...")
		member.Phone = extractNumber(member.UID)
	}

	if member.Phone == "" {
		fmt.Println("--> return.... uid: " + member.UID + ", name: " + member.Name)
		return
	}

	check, err := db.Query("SELECT member_id from members where phone = ?", member.Phone)
	utils.CheckErr(err)

	memberJson, _ := json.Marshal(member)
	if len(check) == 0 {
		memberType, err := db.Query("select type_id from members_type where point_target=0 and spent_target=0 and price=0 and admin_fkid = ? ", adminId)
		if utils.CheckErr(err) {
			return
		}
		if len(memberType) == 0 {
			utils.SendMessageToSlack(fmt.Sprintf("[%s] - '%s' has no default member type", os.Getenv("server"), adminId))
			return
		}
		gender := 1
		if strings.ToLower(member.Gender) == "female" {
			gender = 0
		}
		resp, err := db.Insert("members", map[string]any{
			//"type_fkid":  memberType["type_id"],
			"name":         member.Name,
			"gender":       gender,
			"phone":        member.Phone,
			"address":      member.City,
			"status":       1,
			"data_created": time.Now().Unix() * 1000,
			//"admin_fkid": adminId,
		})
		utils.CheckErr(err)

		memberId, _ := resp.LastInsertId()
		_, err = db.Insert("member_behave", map[string]any{
			"member_fkid": memberId,
			"info":        string(memberJson),
		})
		utils.CheckErr(err)

		_, err = db.Insert("members_detail", map[string]any{
			"member_fkid":   memberId,
			"admin_fkid":    adminId,
			"type_fkid":     memberType["type_id"],
			"register_date": time.Now().Unix() * 1000,
		})
		utils.CheckErr(err)

		//check if something not right
		data, err := db.Query("select count(*) as qty from members m join member_behave mb on m.member_id = mb.member_fkid where name = ?", member.Name)
		utils.CheckErr(err)
		if utils.ToInt(data["qty"]) >= 5 {
			utils.SendMessageToSlack(fmt.Sprintf("[%s] - Something Not Right %s is occur %v times", os.Getenv("server"), member.Name, data["qty"]))
		}

	} else {
		//update member data...
		_, err = db.UpdateDb("member_behave", map[string]any{
			"info": string(memberJson),
		}, map[string]any{
			"member_fkid": check["member_id"],
		})
		utils.CheckErr(err)
	}
}

func extractNumber(uid string) string {
	extractor := []int{17, 18, 21, 22, 23, 26, 27, 28, 30, 31, 32, 33}
	phone := ""
	for index, num := range uid {
		for _, ext := range extractor {
			if index == ext {
				phone += fmt.Sprintf("%c", num)
			}
		}
	}
	return phone
}

func UpdateOrderSales(ctx *fasthttp.RequestCtx) {
	employeeId := string(ctx.PostArgs().Peek("employee_id"))
	response := models.ResponseAny{Millis: time.Now().UnixNano() / 1000000, Status: true}
	sales := models.OrderSales{}
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&sales)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	// _, err = db.Update("order_sales", map[string]any{
	// 	"time_modified":      time.Now().UnixNano() / 1000000,
	// 	"time_accept_reject": sales.TimeAcceptReject,
	// 	"time_ready":         sales.TimeReady,
	// 	"time_taken":         sales.TimeTaken,
	// 	"status":             sales.Status,
	// 	"reject_reason":      sales.RejectReason,
	// }, "order_sales_id = ?", sales.OrderSalesId)

	// if log.IfError(err) {
	// 	response.Status = false
	// }

	orderSales, err := db.Query(`SELECT o.admin_fkid, os.id from order_sales os 
	join outlets o on o.outlet_id=os.outlet_fkid
	where os.order_sales_id = ?`, sales.OrderSalesId)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return
	}

	if len(orderSales) == 0 {
		ctx.SetStatusCode(fasthttp.StatusNotFound)
		return
	}

	err = db.WithTransaction(func(t db.Transaction) error {
		t.Update("order_sales", map[string]any{
			"time_modified":      time.Now().UnixNano() / 1000000,
			"time_accept_reject": sales.TimeAcceptReject,
			"time_ready":         sales.TimeReady,
			"time_taken":         sales.TimeTaken,
			"status":             sales.Status,
			"reject_reason":      sales.RejectReason,
		}, "order_sales_id = ?", sales.OrderSalesId)

		t.Insert("order_sales_status_log", array.RemoveEmpty(map[string]any{
			"order_sales_id": sales.OrderSalesId,
			"status":         sales.Status,
			"admin_id":       orderSales["admin_fkid"],
			"employee_id":    employeeId,
			"time_created":   time.Now().UnixNano() / 1000000,
		}))

		return nil
	})

	if log.IfError(err) {
		response.Status = false
	}

	//if from behave, send update to server
	if strings.HasPrefix(sales.OrderSalesId, utils.BEHAVE_ORDER_PREFIX) {
		go behave.UpdateOrderToBehave(sales)
	} else {
		log.Info("this order '%s' will not send to behave", sales.OrderSalesId)
	}

	google.PublishMessage(map[string]any{
		"status":         sales.Status,
		"order_sales_id": sales.OrderSalesId,
		"creator":        fmt.Sprintf("admin:%v", orderSales["admin_fkid"]),
	}, fmt.Sprintf("crm-order-status-%s", os.Getenv("ENV")))

	_ = json.NewEncoder(ctx).Encode(response)
}

func ForceSendRecapAllOutlet(ctx *fasthttp.RequestCtx) {
	adminId := ctx.UserValue("adminId")
	openShiftId := cast.ToInt(string(ctx.QueryArgs().Peek("openShiftId")))
	log.Info("force send recap all outlet adminId: %v, openShiftId: %v", adminId, openShiftId)

	sql := `
select os.open_shift_id, os.outlet_fkid, from_unixtime(time_open / 1000 + 25200, '%d-%m-%Y') as tanggal
from open_shift os
         join outlets o on os.outlet_fkid = o.outlet_id
where admin_fkid = @adminId
$WHERE
order by os.time_close desc
limit 1
`

	var sqlWhere strings.Builder
	if openShiftId > 0 {
		sqlWhere.WriteString(" and os.open_shift_id = @openShiftId ")
	}

	sql, params := db.MapParam(sql, map[string]any{
		"adminId":     adminId,
		"openShiftId": openShiftId,
		"WHERE":       sqlWhere.String(),
	})

	data, err := db.Query(sql, params...)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	go SendRecapAllOutlet(models.CashRecap{OpenShiftFkid: utils.ToInt(data["open_shift_id"]), OutletFkid: utils.ToInt(data["outlet_fkid"])})
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Millis: time.Now().UnixNano() / 1000000, Status: true, Data: data})
}

func GetSalesNotes(ctx *fasthttp.RequestCtx) {
	adminId := utils.ToInt(ctx.Request.Header.Peek("admin_id"))

	// // notesCache := make(map[string]any)
	// var notesCache models.SalesNote
	// filter := bson.D{{Key: "admin_id", Value: adminId}}
	// mongoDb := db.MongoDbClient().Database("products")
	// err := mongoDb.Collection("notes").FindOne(context.Background(), filter).Decode(&notesCache)
	// if err == nil && notesCache.Data != "" {
	// 	minStart := time.Now().AddDate(0, 0, -7).Unix() * 1000
	// 	if notesCache.CreatedAt > minStart {
	// 		var productNotes []models.ProductNotes
	// 		err = json.Unmarshal([]byte(notesCache.Data), &productNotes)
	// 		// log.Info("notes mongoDb: %v | createdAt: %v", len(result), notesCache.CreatedAt)
	// 		if !log.IfError(err) && len(productNotes) > 0 {
	// 			if len(productNotes) > maxData {
	// 				log.Info("total notes is too high: %v", len(productNotes))
	// 				productNotes = productNotes[:maxData]
	// 			}
	// 			_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Millis: time.Now().UnixNano() / 1000000, Status: true, Data: productNotes})
	// 			return
	// 		}
	// 	}
	// } else {
	// 	log.Info("no data from mongodb..., err: %v", err)
	// }

	// //this query is very slow...
	// sql := `select (note) as note, count(*) as total, product_fkid
	// from sales_detail sd
	//         join products p on sd.product_fkid = p.product_id
	// where sd.time_created > ? and length((note)) > 2
	//  and p.admin_fkid = ?
	// group by note, product_fkid
	// order by total desc, product_fkid `

	// timeStart := time.Now().AddDate(0, -3, 0).Unix() * 1000
	// notes, err := db.QueryArray(sql, timeStart, adminId)
	// log.IfError(err)

	// result := make([]models.ProductNotes, 0)

	// if len(notes) == 0 {
	// 	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Millis: time.Now().UnixNano() / 1000000, Status: true, Data: result})
	// 	return
	// }

	// notesArray := make([]string, 0)
	// for i, note := range notes {
	// 	txtNote := strings.TrimSpace(utils.ToString(note["note"]))
	// 	if len(txtNote) <= 2 {
	// 		continue
	// 	}
	// 	notesArray = append(notesArray, txtNote)
	// 	if i == len(notes)-1 || note["product_fkid"] != notes[i+1]["product_fkid"] {
	// 		result = append(result, models.ProductNotes{
	// 			ProductID: cast.ToInt(note["product_fkid"]),
	// 			Notes:     notesArray,
	// 		})
	// 		notesArray = make([]string, 0)
	// 	}
	// }

	// log.Info("total notes: %d, adminId: %v", len(result), adminId)
	// if len(result) > maxData {
	// 	result = result[:maxData]
	// }

	// go func(adminId, result any) {
	// 	if result == nil {
	// 		return
	// 	}
	// 	dataJson, err := json.Marshal(result)
	// 	log.IfError(err)

	// 	_, err = mongoDb.Collection("notes").DeleteOne(context.Background(), bson.D{{Key: "admin_id", Value: adminId}})
	// 	fmt.Println(err)

	// 	res, err := mongoDb.Collection("notes").InsertOne(context.Background(), map[string]any{
	// 		"created_at": time.Now().Unix() * 1000,
	// 		"admin_id":   adminId,
	// 		"data":       string(dataJson),
	// 	})
	// 	if !log.IfError(err) {
	// 		fmt.Printf("notes inserted with id: %v \n", res.InsertedID)
	// 	}

	// }(adminId, result)

	// _ = json.NewEncoder(ctx).Encode(models.ResponseAny{Millis: time.Now().UnixNano() / 1000000, Status: true, Data: result})

	salesNotes, err := fetchSalesNoteCached(adminId)
	if err != nil || len(salesNotes) == 0 {
		log.Info("fetch notes from cache (%v) err: %v | length: %v", adminId, err, len(salesNotes))
		// salesNotes, _ = fetchSalesNote(adminId)
		// go cacheSalesNote(adminId, salesNotes)
	}
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Millis: time.Now().UnixNano() / 1000000, Status: true, Data: salesNotes})
}

func NotifyNewOrderSales(salesId string, outletId int) {
	// Get device tokens
	sql := `select firebase_token 
			from devices 
			where outlet_fkid = ? 
			and firebase_token is not null and firebase_token != '' 
			order by last_sync desc`

	devices, err := db.QueryArray(sql, outletId)
	if log.IfError(err) {
		return
	}

	if len(devices) == 0 {
		return
	}

	for _, device := range devices {
		err = google.SendNotification(google.NotificationData{
			Token: cast.ToString(device["firebase_token"]),
			Data: map[string]string{
				"type": "action_sync",
			},
		})
		log.IfError(err)

		err = google.SendNotification(google.NotificationData{
			Token: cast.ToString(device["firebase_token"]),
			Data: map[string]string{
				"message": fmt.Sprintf("pembayaran pesanan %s telah tekonfirmasi", salesId),
				"title":   fmt.Sprintf("%s Berhasil Dibayarkan", salesId),
			},
		})
		log.IfError(err)
	}
	fmt.Println("notif sent to: ", len(devices), "devices")
}
