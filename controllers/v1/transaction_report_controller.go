package v1

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/google"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"go.mongodb.org/mongo-driver/bson"
)

// get report shift from db
func GetShiftReportFromCachedDb(openShiftId int) models.CloseShiftReport {
	shiftReport := models.CloseShiftReport{}
	filter := bson.D{{Key: "open_shift_id", Value: openShiftId}}
	//filter := bson.M{"date_time": bson.M{"$gt": 1616384881000, "$lt": 1616384892000}}
	err := db.Mongo().Collection("close_shift").FindOne(context.Background(), filter).Decode(&shiftReport)
	log.IfError(err)
	log.Info("get shift report, openShiftId: %d - sales total: %d", openShiftId, shiftReport.ItemSalesTotal)
	return shiftReport
}

func GetCloseShiftAdminAndDate(adminId int, date string) []models.CloseShiftReport {
	log.Info("getCloseShiftAdminAndDate %v | %v", adminId, date)
	filter := bson.M{
		"admin.admin_id": adminId,
		"date":           bson.M{"$regex": fmt.Sprintf(".*%s.*", date)},
	}

	var shiftReport []models.CloseShiftReport
	collection := db.Mongo().Collection("close_shift")
	cursor, err := collection.Find(context.Background(), filter)
	if log.IfError(err) {
		return shiftReport
	}

	defer cursor.Close(context.Background())
	for cursor.Next(context.Background()) {
		var result models.CloseShiftReport
		err := cursor.Decode(&result)
		if log.IfError(err) {
			return shiftReport
		}
		shiftReport = append(shiftReport, result)
	}

	log.IfError(cursor.Err())
	fmt.Println("--size of closeShift: ", len(shiftReport))
	return shiftReport
}

func SendRecapAllOutlet(recap models.CashRecap) {
	data, err := db.Query("select time_open from open_shift where open_shift_id = ?", recap.OpenShiftFkid)
	log.IfError(err)

	if len(data) == 0 {
		log.Info("no data open shift with id %v", recap.OpenShiftFkid)
		return
	}

	dateSecond := utils.ToInt64(data["time_open"])

	sql := `
select cr.*, s.name as shift_name, o.name as outlet_name, o.outlet_id, a.business_name, a.admin_id
from cash_recap cr
         join open_shift os on cr.open_shift_fkid = os.open_shift_id
         join outlets o on cr.outlet_fkid = o.outlet_id
         join admin a on o.admin_fkid = a.admin_id
         join shift s on os.shift_fkid = s.shift_id
where from_unixtime(os.time_open / 1000 + 25200, '%d-%m-%Y') = from_unixtime(? / 1000 + 25200, '%d-%m-%Y')
  and s.admin_fkid = (select admin_fkid
                      from open_shift ops
                               join outlets o2 on ops.outlet_fkid = o2.outlet_id
                      where ops.open_shift_id = ? )
order by os.outlet_fkid`

	cashRecaps, err := db.QueryArray(sql, dateSecond, recap.OpenShiftFkid)
	if log.IfError(err) {
		return
	}
	if len(cashRecaps) == 0 {
		return
	}

	reportResult := ""
	phone := ""
	adminId := ""
	businessName := ""
	totalRecapToday := 0
	totalPerOutlet := 0
	shiftCounter := 0
	cashRecapReport := make([]models.CashRecapReport, 0)

	// Convert milliseconds to time.Time
	t := time.Unix(0, dateSecond*int64(time.Millisecond))
	// Reduce 1 day
	t = t.Add(-24 * time.Hour)
	// Format the date as "dd-MM-yyyy"
	prevDate := t.Format("02-01-2006")
	prevCloseShift := GetCloseShiftAdminAndDate(cast.ToInt(cashRecaps[0]["admin_id"]), prevDate)
	prevCloseShiftByOutlet := make(map[int]int)
	for _, close := range prevCloseShift {
		prevCloseShiftByOutlet[close.Outlet.OutletId] += close.ItemSalesTotal
	}
	log.Info("total from prev data: %v", len(prevCloseShift))

	for index, cashRecap := range cashRecaps {
		cashRecapId := cashRecap["cash_recap_id"]
		delete(cashRecap, "cash_recap_id")
		jsonByte, err := json.Marshal(cashRecap)
		log.IfError(err)

		recapCurrent := models.CashRecap{}
		err = json.Unmarshal(jsonByte, &recapCurrent)
		log.IfError(err)
		recapCurrent.CashRecapID = utils.ToInt64(cashRecapId)

		//report := CalculateShiftReport(recapCurrent)
		report := GetShiftReportFromCachedDb(recapCurrent.OpenShiftFkid)
		if report.DateTime == 0 { //just, in case failed to get from db, do the old fashion
			log.Error("failed to get shift from mongo-db (report all outlet)")
			report = CalculateShiftReport(recapCurrent)
		}

		if index == 0 {
			phone = utils.ToString(report.Admin["phone"])
			adminId = utils.ToString(report.Admin["admin_fkid"])
			businessName = utils.ToString(cashRecap["business_name"])
		}

		if reportResult == "" {
			reportResult += fmt.Sprintf("*%s* \n", strings.TrimSpace(utils.ToString(cashRecap["outlet_name"])))
		}

		shiftCounter++
		totalPerOutlet += report.ItemSalesTotal
		totalRecapToday += report.ItemSalesTotal
		reportResult += fmt.Sprintf("Shift %v : %s \n", cashRecap["shift_name"], utils.CurrencyFormat(report.ItemSalesTotal))

		//add total
		if index == (len(cashRecaps)-1) || cashRecap["outlet_id"] != cashRecaps[index+1]["outlet_id"] {
			if shiftCounter > 1 {
				reportResult += fmt.Sprintf("*TOTAL*     *%s*\n", utils.CurrencyFormat(totalPerOutlet))
			}
			cashRecapReport = append(cashRecapReport, models.CashRecapReport{Total: totalPerOutlet, Report: reportResult, OutletId: cast.ToInt(cashRecap["outlet_id"])})
			totalPerOutlet = 0
			shiftCounter = 0
			reportResult = ""
		}
	}

	if len(cashRecapReport) == 0 {
		log.Info(">> NO REPORT <<")
		return
	}

	//sort descending
	sort.Slice(cashRecapReport, func(i, j int) bool {
		return cashRecapReport[j].Total < cashRecapReport[i].Total
	})

	for rank, report := range cashRecapReport {
		rate := ""
		if totalPrev, ok := prevCloseShiftByOutlet[report.OutletId]; ok {
			if report.Total > totalPrev {
				rate = "⬆️ "
			} else if report.Total < totalPrev {
				rate = "⬇️ "
			} else {
				rate = "↕️ "
			}
		}
		reportResult += fmt.Sprintf("%s*#%d.* %s \n", rate, rank+1, report.Report)
	}

	reportResult += fmt.Sprintf("\n\n*TOTAL SELURUH OUTLET*   *%v*", utils.CurrencyFormat(totalRecapToday))
	reportResult = fmt.Sprintf("UNIQ POS - REKAP LAPORAN TUTUP KASIR SELURUH OUTLET *%s* \n"+
		"_Tanggal : %v_ \n\n%s", strings.ToUpper(businessName), time.Unix(dateSecond/1000, 0).Format("02-01-2006"), reportResult)

	log.Info("REKAP LAPORAN TUTUP KASIR SELURUH OUTLET *%s* ", strings.ToUpper(businessName))
	log.Info("send to %v", phone)

	//_ = utils.SendWhatsAppMessage(reportResult, "secure", adminId, utils.Encrypt(phone))
	//_ = utils.SendMessageToGateWay(reportResult, phone, 0) //param adminId set 0, we wan this sent through UNIQ whatsapp number

	//identifierId is used to check whether recap has sent
	//take a look at file: job/report_job.go @SendAllOutletReport()

	identifierId := fmt.Sprintf("recapalloutlet_%s_%s", adminId, time.Unix(dateSecond/1000, 0).Format("02-01-2006"))
	scheduledMessage := map[string]interface{}{
		"title":         "RECAP SHIFT REPORT",
		"message":       reportResult,
		"media":         "whatsapp",
		"receiver":      phone,
		"identifier_id": identifierId,
		"time_deliver":  time.Now().Unix() * 1000,
		"data_created":  time.Now().Unix() * 1000,
	}

	err = google.PublishMessage(scheduledMessage, "messaging-gateway-production")

	//still insert to scheduled_message, for checking (take a look at file: job/report_job.go @SendAllOutletReport())
	//but if successfully sent to pubsub, remove unnecessary data (we care for identifier_id only)
	if err == nil {
		scheduledMessage["status"] = "sent"
		scheduledMessage["message"] = ""
	}
	_, err = db.Insert("scheduled_message", scheduledMessage)
	log.IfError(err)

	////send to others
	sql = `
select DISTINCT rra.address, rra.media
from report_recipient rr
         join report_recipient_type rrt on rr.report_recipient_id = rrt.report_recipient_fkid
         join report_recipient_outlet rro on rr.report_recipient_id = rro.report_recipient_fkid
         join report_recipient_address rra on rr.report_recipient_id = rra.report_recipient_fkid
where admin_fkid = ? and report_type_fkid='recap_outlet' and media='whatsapp' `
	receivers, err := db.QueryArray(sql, adminId)
	if log.IfError(err) {
		return
	}

	log.Info("total receivers report : %d", len(receivers))
	for _, receiver := range receivers {
		//_ = utils.SendWhatsAppMessage(reportResult, "secure", "", utils.Encrypt(utils.ToString(receiver["address"])))
		log.Info("recap all outlet '%s' send also to : %v", businessName, receiver["address"])
		//_ = utils.SendMessageToGateWay(reportResult, utils.ToString(receiver["address"]), 0)

		scheduledMessage = map[string]interface{}{
			"title":         "RECAP SHIFT REPORT",
			"message":       reportResult,
			"media":         "whatsapp",
			"identifier_id": identifierId,
			"receiver":      utils.ToString(receiver["address"]),
			"time_deliver":  time.Now().UnixMilli(),
			"data_created":  time.Now().UnixMilli(),
			"status":        "pending",
		}
		// err = google.PublishMessage(scheduledMessage, "messaging-gateway-production")
		// if err == nil {
		// 	//if not error, set status to sent so messaging-gateway will not send again
		// 	scheduledMessage["status"] = "sent"
		// 	scheduledMessage["message"] = ""
		// }
		_, err = db.Insert("scheduled_message", scheduledMessage)
		log.IfError(err)
	}

	//save to mongodb
	// _, err = db.Mongo().Collection("close_shift_all").InsertOne(context.Background(), map[string]interface{}{
	// 	"created_at": utils.CurrentMillis(),
	// 	"admin_id":   cast.ToInt(adminId),
	// 	"report":     cashRecapReport,
	// })
	// log.IfError(err)
}
