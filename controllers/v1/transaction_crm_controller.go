package v1

import (
	"encoding/json"
	"fmt"
	"math"
	"os"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/core/utils/array"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

func HandleOrderSales(orderSalesId string) models.ResponseAny {
	var result models.ResponseAny

	//check status, we only care for paid order
	//where status = 'payment_verified' : in case paid status is not the last
	sql := `SELECT status
FROM   order_sales_status_log
WHERE  order_sales_id = ? and status = 'payment_verified'
ORDER  BY id DESC
LIMIT  1`
	orderStatus, err := db.Query(sql, orderSalesId)
	log.IfError(err)
	if utils.ToString(orderStatus["status"]) != "payment_verified" {
		result.Message = fmt.Sprintf("order '%s' is not paid", orderSalesId)
		result.Code = 20
		return result
	}

	//skip if order already inserted into sales
	salesCheck, err := db.QueryArray("select sales_id from sales where sales_id = ? or display_nota = ? ", orderSalesId, orderSalesId)
	if !log.IfError(err) && len(salesCheck) > 0 {
		result.Message = fmt.Sprintf("order '%s' already inserted into sales", orderSalesId)
		result.Code = 21
		return result
	}

	sql = `SELECT os.*, o.admin_fkid, m.name as member_name FROM order_sales os
join outlets o on os.outlet_fkid = o.outlet_id
join members m on m.member_id=os.member_fkid
where order_sales_id = ?`
	order, err := db.Query(sql, orderSalesId)
	if log.IfError(err) {
		result.Message = err.Error()
		return result
	}

	adminId := utils.ToInt(order["admin_fkid"])
	var sales models.Sale
	err = json.Unmarshal([]byte(utils.ToString(order["items"])), &sales)
	if log.IfError(err) {
		log.Info("failed parsing items: %s", utils.ToString(order["items"]))
	}

	//get open shift id
	sql = `SELECT open_shift_id FROM open_shift
where outlet_fkid = ?
order by time_open desc
limit 1`
	openShift, err := db.Query(sql, order["outlet_fkid"])
	if log.IfError(err) {
		result.Message = err.Error()
		return result
	}

	if len(openShift) == 0 {
		id, err := getOrCreateOpenShift(adminId, cast.ToInt(order["outlet_fkid"]))
		log.Info("getOrCreateOpenShift: %v | err: %v", id, err)
		if err != nil {
			result.Message = "no open shift found"
			return result
		}
		sales.OpenShiftID = id
	} else {
		sales.OpenShiftID = utils.ToInt(openShift["open_shift_id"])
	}

	sales.Status = "success"
	sales.NoNota = orderSalesId
	sales.Payment = "CARD"
	sales.Customer = utils.ToString(order["member_name"])
	//sales.Payments

	if len(sales.NoNota) > 20 {
		sales.NoNota = sales.NoNota[len(sales.NoNota)-20:]
	}
	sales.DisplayNota = sales.NoNota

	//product: price_buy, price_sell
	productDetailIds := make([]interface{}, 0)
	for i, item := range sales.OrderList {
		if item.Product.PriceBuy == 0 {
			productDetailIds = append(productDetailIds, item.Product.ProductDetailID)
		}
		for j, extra := range item.Extra {
			if extra.Product.ProductID == 0 && extra.Product.ProductFkid > 0 {
				sales.OrderList[i].Extra[j].Product.ProductID = extra.Product.ProductFkid
			}
			if extra.Product.ProductDetailID == 0 && extra.Product.ProductDetailFkid > 0 {
				sales.OrderList[i].Extra[j].Product.ProductDetailID = extra.Product.ProductDetailFkid
			}
		}
		if item.Product.ProductDetailID == 0 && item.Product.ProductDetailFkid > 0 {
			sales.OrderList[i].Product.ProductDetailID = item.Product.ProductDetailFkid
		} else if item.Product.ProductDetailID == 0 && item.Product.ProductDetailFkid == 0 {
			log.Info("[WARNING] both are 0. product_detail_id: %d, product_detail_fkid: %d, item: %v", item.Product.ProductDetailID, item.Product.ProductDetailFkid, cast.ToJson(item.Product))
		}
		if item.Product.ProductID == 0 && item.Product.ProductFkid > 0 {
			sales.OrderList[i].Product.ProductID = item.Product.ProductFkid
		} else if item.Product.ProductID == 0 && item.Product.ProductFkid == 0 {
			//log both are 0
			log.Info("[WARNING] both are 0. product_id: %d, product_fkid: %d, item: %v", item.Product.ProductID, item.Product.ProductFkid, cast.ToJson(item.Product))
			productDetailIds = append(productDetailIds, item.Product.ProductDetailID) //add this, so it will set later
		}
	}

	if len(productDetailIds) > 0 {
		sql = `select product_detail_id, product_fkid, price_buy, price_sell from products_detail where product_detail_id in ($PARAM?)`
		sql = strings.Replace(sql, "$PARAM", strings.Repeat("?,", len(productDetailIds)-1), 1)
		products, err := db.QueryArray(sql, productDetailIds...)
		log.IfError(err)

		for _, product := range products {
			for i, item := range sales.OrderList {
				if item.Product.ProductDetailID == utils.ToInt(product["product_detail_id"]) {
					sales.OrderList[i].Product.PriceBuy = utils.ToInt(product["price_buy"])
					sales.OrderList[i].Product.ProductFkid = utils.ToInt(product["product_fkid"])
					sales.OrderList[i].Product.ProductID = utils.ToInt(product["product_fkid"])
					sales.OrderList[i].Product.ProductDetailID = item.Product.ProductDetailID
					break
				}
			}
		}
	}

	//validate data
	// for i, item := range sales.OrderList {

	// }

	//setup payment
	var paymentInfo struct {
		Name string `json:"name,omitempty"`
		Id   string `json:"id,omitempty"`
	}
	log.IfError(json.Unmarshal([]byte(utils.ToString(order["payment_info"])), &paymentInfo))
	bankName := paymentInfo.Name
	if bankName == "" {
		bankName = paymentInfo.Id
	}

	paymentNotif, err := db.Query("select payment_gateway from payment_notification where order_id = ? limit 1", orderSalesId)
	log.IfError(err)
	log.Info("payment_gateway of '%v' : %v", orderSalesId, paymentNotif)

	providerKey := ""
	var paymentBank map[string]interface{}
	if len(paymentNotif) > 0 { //if not found in payment notif, its because using manual transfer
		providerKey = strings.ToLower(strings.ReplaceAll(bankName, " ", "_"))
		paymentBank, err = db.Query("select bank_id from payment_media_bank where provider_payment_key = ? and admin_fkid = ?", bankName, adminId)
		log.IfError(err)
		bankName = strings.ToUpper(fmt.Sprintf("%s %s", bankName, paymentNotif["payment_gateway"]))
	} else {
		//if not using payment gateway, get the regular bank
		paymentBank, err = db.Query("select bank_id from payment_media_bank where name = ? and admin_fkid = ?", bankName, adminId)
		log.IfError(err)
	}

	paymentMediaBankId := int64(0)
	if len(paymentBank) == 0 {
		resp, err := db.Insert("payment_media_bank", map[string]interface{}{
			"name":                 bankName,
			"no_rekening":          0,
			"admin_fkid":           adminId,
			"data_created":         time.Now().Format("2006-01-02 15:04:05"),
			"data_modified":        time.Now().Format("2006-01-02 15:04:05"),
			"data_status":          "on",
			"provider_payment_key": providerKey,
			"provider":             paymentNotif["payment_gateway"],
		})
		if log.IfError(err) {
			result.Message = err.Error()
			return result
		}
		paymentMediaBankId, _ = resp.LastInsertId()

		//insert to bank detail
		outlets, err := db.QueryArray("select outlet_id from outlets where admin_fkid = ? and data_status='on'", adminId)
		log.IfError(err)

		err = db.WithTransaction(func(t db.Transaction) error {
			for _, outlet := range outlets {
				t.Insert("payment_media_bank_detail", map[string]interface{}{
					"bank_fkid":     paymentMediaBankId,
					"outlet_fkid":   outlet["outlet_id"],
					"active_on_pos": 1,
					"active_on_crm": 1,
					"data_status":   "on",
					"data_modified": time.Now().Format("2006-01-02 15:04:05"),
				})
			}
			return nil
		})
		log.IfError(err)

	} else {
		paymentMediaBankId = utils.ToInt64(paymentBank["bank_id"])
	}

	//setup services
	sales.Taxes = make([]models.Taxes, 0)
	shippingCharge := 0
	deliveryTypes := []string{"delivery", "internal_delivery"}
	if array.Contain(deliveryTypes, utils.ToString(order["order_type"])) {
		shipping, err := db.Query("select * from order_sales_shipments where order_sales_id = ?", orderSalesId)
		log.IfError(err)

		shippingCharge = utils.ToInt(shipping["shipping_charge"])
		if shippingCharge > 0 {
			serviceShipping := getOrAddServiceByName("Ongkos Kirim", shippingCharge, adminId)
			sales.Taxes = append(sales.Taxes, serviceShipping)
		}
	}

	totalPromo := 0
	for _, promo := range sales.Promotions {
		totalPromo += utils.ToInt(promo.Value)
	}

	sales.GrandTotal -= totalPromo
	sales.GrandTotal += sales.UniqueCode
	sales.GrandTotal += shippingCharge
	sales.TimeCreated = time.Now().Unix() * 1000
	sales.MemberId = utils.ToString(order["member_fkid"])

	if sales.UniqueCode > 0 {
		serviceUniqueCode := getOrAddServiceByName("Kode Unik Transaksi", sales.UniqueCode, adminId)
		sales.Taxes = append(sales.Taxes, serviceUniqueCode)
	}

	sales.Payments = make([]models.Payment, 0)
	sales.Payments = append(sales.Payments, models.Payment{
		Method: "CARD",
		Total:  sales.GrandTotal,
		Pay:    sales.GrandTotal,
		Bank: models.Bank{
			BankID: utils.ToInt(paymentMediaBankId),
		},
	})

	//validate grand total
	go func(sales models.Sale, shippingCharge int) {
		validationGrandTotal := 0
		for _, order := range sales.OrderList {
			validationGrandTotal += order.Price * order.Qty
			for _, tax := range order.Taxes {
				validationGrandTotal += tax.Total
			}
		}
		for _, promo := range sales.Promotions {
			validationGrandTotal -= utils.ToInt(promo.Value)
		}
		validationGrandTotal += sales.UniqueCode
		validationGrandTotal += shippingCharge

		log.Info("saved: %d | validation : %d", sales.GrandTotal, validationGrandTotal)
		if sales.GrandTotal != validationGrandTotal && math.Abs(float64(sales.GrandTotal-validationGrandTotal)) > 100 {
			log.IfError(fmt.Errorf("invalid grand total from order: %s ", sales.NoNota))
			log.Info(utils.SimplyToJson(order))
		}
	}(sales, shippingCharge)

	sales.SalesTag.SalesTagID = int(getOrCreateSalesTagId(cast.ToString(order["order_type"]), adminId))

	response, err := SaveSales(sales, utils.ToString(order["admin_fkid"]), "")
	if log.IfError(err) {
		result.Message = err.Error()
		return result
	}

	if response.Status {
		go NotifyNewOrderSales(sales.NoNota, sales.OutletID)
	}
	return response
}

func getOrCreateSalesTagId(orderType string, adminId int) int64 {
	orderType = strings.ReplaceAll(orderType, "_", " ")
	tag, err := db.Query("select sales_tag_id from sales_tag where name = ? and admin_fkid = ?", orderType, adminId)
	if log.IfError(err) {
		return 0
	}
	if len(tag) > 0 {
		return cast.ToInt64(tag["sales_tag_id"])
	}

	resp, err := db.Insert("sales_tag", map[string]interface{}{
		"name":          orderType,
		"admin_fkid":    adminId,
		"data_created":  time.Now().UnixMilli(),
		"data_modified": time.Now().UnixMilli(),
		"data_status":   "on",
	})
	if log.IfError(err) {
		return 0
	}
	id, _ := resp.LastInsertId()
	return id
}

func updateMemberSpend(sales models.Sale, adminId int) {
	if sales.MemberId == "" {
		log.Info("sales %v has no member", sales.NoNota)
		return
	}
	totalTrx := 1
	//handle if refund
	if strings.ToLower(sales.Status) == "refund" {
		totalTrx = -1
		sales.GrandTotal = -sales.GrandTotal
	}

	//update members_detail
	_, err := db.GetDb().Exec(`update members_detail 
	set total_spend=total_spend+?, total_transaction=total_transaction+?
	where member_fkid = ? and admin_fkid = ?`, sales.GrandTotal, totalTrx, sales.MemberId, adminId)
	log.IfError(err)
}

func GetOrderSales(ctx *fasthttp.RequestCtx) {
	response := models.ResponseArray{Status: true, Data: make([]map[string]any, 0), Millis: (time.Now().Unix() * 1000) - 5000}

	if os.Getenv("server") == "production" {
		_ = json.NewEncoder(ctx).Encode(response)
		return
	}

	if !auth.ValidateOutlet(ctx) {
		return
	}

	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")

	sql := `SELECT os.*, m.name as member_name, m.phone, m.email
	FROM order_sales os 
	left join members m on m.member_id = os.member_fkid 
	WHERE outlet_fkid = ? AND time_modified > ?`

	data, err := db.QueryArray(sql, outletId, lastSync)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	for i, row := range data {
		if row["member_name"] != nil {
			data[i]["member"] = map[string]any{
				"member_id": row["member_fkid"],
				"name":      row["member_name"],
				"phone":     row["phone"],
				"email":     row["email"],
			}
		} else {
			//meta_data example: {"name": "Roger", "phone": "0856339874983", "table": "12"}
			//use meta_data if exist
			if metaDataJson := cast.ToString(row["meta_data"]); metaDataJson != "" {
				var metaData map[string]any
				if log.IfError(json.Unmarshal([]byte(metaDataJson), &metaData)) {
					continue
				}
				data[i]["meta_data"] = metaData
			}
		}
	}

	response.Data = data
	_ = json.NewEncoder(ctx).Encode(response)
}
