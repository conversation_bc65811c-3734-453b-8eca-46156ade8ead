package cast

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"runtime"
	"strconv"
)

func ToInt(value interface{}) int {
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case int:
		return v
	case int8:
		return int(v)
	case int16:
		return int(v)
	case int32:
		return int(v)
	case int64:
		return int(v)
	case uint:
		return int(v)
	case uint8:
		return int(v)
	case uint16:
		return int(v)
	case uint32:
		return int(v)
	case uint64:
		return int(v)
	case float32:
		return int(v)
	case float64:
		return int(v)
	case string:
		if parsedInt, err := strconv.ParseInt(v, 10, 64); err == nil {
			return int(parsedInt)
		} else {
			fmt.Printf("Error converting %v to int: %v\n", v, err)
		}
	default:
		fmt.Printf("Error converting %v to int: Unsupported type\n", reflect.TypeOf(value))
	}
	return 0
}

func ToString(data interface{}) string {
	if data == nil {
		return ""
	}

	switch v := data.(type) {
	case int:
		return strconv.Itoa(v)
	case int32:
		return strconv.Itoa(int(v))
	case int64:
		return strconv.FormatInt(v, 10)
	case float64:
		return strconv.FormatFloat(v, 'f', 2, 64)
	case float32:
		return strconv.FormatFloat(float64(v), 'f', -1, 32)
	case []uint8:
		return string(v)
	case string:
		return v
	default:
		_, fn, line, _ := runtime.Caller(1)
		fmt.Printf("%s:%d - can't convert to string '%v' : type %v \n", fn, line, data, reflect.TypeOf(data))
		return ""
	}
}

func ToFloat(data interface{}) float64 {
	dataStr := ToString(data)
	result, err := strconv.ParseFloat(dataStr, 64)
	if err != nil {
		//fmt.Println("Converting string to float error", err, "Data : ", data)
		return 0
	}
	return result
}

func ToFloat32(value interface{}) float32 {
	switch v := value.(type) {
	case float32:
		return v
	case float64:
		return float32(v)
	case int:
		return float32(v)
	case int8:
		return float32(v)
	case int16:
		return float32(v)
	case int32:
		return float32(v)
	case int64:
		return float32(v)
	case uint:
		return float32(v)
	case uint8:
		return float32(v)
	case uint16:
		return float32(v)
	case uint32:
		return float32(v)
	case uint64:
		return float32(v)
	case string:
		f, err := strconv.ParseFloat(v, 32)
		if err != nil {
			return 0
		}
		return float32(f)
	default:
		fmt.Println(errors.New("cannot convert value to float32"))
		return 0
	}
}

// func ToInt64(data interface{}) int64 {
// 	if data == nil {
// 		return 0
// 	}

// 	if reflect.TypeOf(data).Kind() == reflect.Float64 {
// 		return int64(data.(float64))
// 	}

// 	dataStr := ToString(data)
// 	if dataStr == "" {
// 		return 0
// 	}

// 	result, err := strconv.ParseInt(dataStr, 10, 64)
// 	if err != nil {
// 		_, fn, line, _ := runtime.Caller(1)
// 		fmt.Printf("%s:%d parsing to int64 err: %v", fn, line, err)
// 		return 0
// 	} else {
// 		return result
// 	}
// }

func ToInt64(value interface{}) int64 {
	switch v := value.(type) {
	case int:
		return int64(v)
	case int8:
		return int64(v)
	case int16:
		return int64(v)
	case int32:
		return int64(v)
	case int64:
		return v
	case uint:
		return int64(v)
	case uint8:
		return int64(v)
	case uint16:
		return int64(v)
	case uint32:
		return int64(v)
	case uint64:
		return int64(v)
	case float32:
		return int64(v)
	case float64:
		return int64(v)
	case string:
		res, err := strconv.ParseInt(v, 10, 64)
		if err != nil {
			fmt.Printf("cannot convert %v to int64\n", reflect.TypeOf(value))
		}
		return res
	default:
		_, fn, line, _ := runtime.Caller(1)
		fmt.Printf("%s:%d cannot convert %v to int64\n", fn, line, reflect.TypeOf(value))
		return 0
	}
}

// func ToFloat32(data interface{}) float32 {
// 	if data == nil {
// 		return 0
// 	}

// 	switch v := data.(type) {
// 	case float32:
// 		return v
// 	case float64:
// 		return float32(v)
// 	}

// 	dataStr := ToString(data)
// 	if dataStr == "" {
// 		_, fn, line, _ := runtime.Caller(1)
// 		fmt.Printf("%v:%v '%v' is empty\n", fn, line, data)
// 		return 0
// 	}
// 	result, err := strconv.ParseFloat(dataStr, 32)
// 	if err != nil {
// 		_, fn, line, _ := runtime.Caller(1)
// 		fmt.Printf("%s:%d parsing to float32 err: %v", fn, line, err)
// 		return 0
// 	} else {
// 		return float32(result)
// 	}
// }

func ToJson(data interface{}) string {
	result, err := json.Marshal(data)
	if err != nil {
		fmt.Println("converting to json err: ", err)
	}
	return string(result)
}

// convert interface to bool
func ToBool(data interface{}) bool {
	if data == nil {
		return false
	}
	if reflect.TypeOf(data).Kind() == reflect.String {
		return data.(string) == "true"
	}
	if reflect.TypeOf(data).Kind() == reflect.Int {
		return data.(int) == 1
	}
	if reflect.TypeOf(data).Kind() == reflect.Int64 {
		return data.(int64) == 1
	}
	return data.(bool)
}

func ToMap(data interface{}) map[string]interface{} {
	if data == nil {
		return map[string]interface{}{}
	}

	switch v := data.(type) {
	case string:
		var result map[string]interface{}
		if err := json.Unmarshal([]byte(v), &result); err != nil {
			_, fn, line, _ := runtime.Caller(1)
			fmt.Printf("%s:%d - can't convert string to map: %v\n", fn, line, err)
			return map[string]interface{}{}
		}
		return result
	case map[string]interface{}:
		return v
	default:
		// Try to convert using reflection for struct types
		val := reflect.ValueOf(data)
		if val.Kind() == reflect.Struct {
			result := make(map[string]interface{})
			for i := 0; i < val.NumField(); i++ {
				field := val.Type().Field(i)
				result[field.Name] = val.Field(i).Interface()
			}
			return result
		}
		_, fn, line, _ := runtime.Caller(1)
		fmt.Printf("%s:%d - can't convert type %v to map\n", fn, line, reflect.TypeOf(data))
		return map[string]interface{}{}
	}
}

func ToArrayMap(data interface{}) []map[string]interface{} {
	if data == nil {
		return []map[string]interface{}{}
	}

	switch v := data.(type) {
	case string:
		var result []map[string]interface{}
		if err := json.Unmarshal([]byte(v), &result); err != nil {
			_, fn, line, _ := runtime.Caller(1)
			fmt.Printf("%s:%d - can't convert string to array map: %v\n", fn, line, err)
			return []map[string]interface{}{}
		}
		return result
	case []map[string]interface{}:
		return v
	case []interface{}:
		result := make([]map[string]interface{}, 0, len(v))
		for _, item := range v {
			if m := ToMap(item); len(m) > 0 {
				result = append(result, m)
			}
		}
		return result
	default:
		// Try to convert single item to array
		if m := ToMap(data); len(m) > 0 {
			return []map[string]interface{}{m}
		}
		_, fn, line, _ := runtime.Caller(1)
		fmt.Printf("%s:%d - can't convert type %v to array map\n", fn, line, reflect.TypeOf(data))
		return []map[string]interface{}{}
	}
}
